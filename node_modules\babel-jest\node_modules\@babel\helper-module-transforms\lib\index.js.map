{"version": 3, "names": ["_assert", "require", "_core", "_helperModuleImports", "_rewriteThis", "_rewriteLiveReferences", "_normalizeAndLoadMetadata", "_dynamicImport", "_getModuleName", "booleanLiteral", "callExpression", "cloneNode", "directive", "directiveLiteral", "expressionStatement", "identifier", "isIdentifier", "memberExpression", "stringLiteral", "valueToNode", "variableDeclaration", "variableDeclarator", "t", "exports", "getDynamicImportSource", "rewriteModuleStatementsAndPrepareHeader", "path", "exportName", "strict", "allowTopLevelThis", "strictMode", "noInterop", "importInterop", "lazy", "esNamespaceOnly", "filename", "constantReexports", "arguments", "loose", "enumerableModuleMeta", "noIncompleteNsImportDetection", "validateImportInteropOption", "assert", "isModule", "node", "sourceType", "meta", "normalizeModuleAndLoadMetadata", "initializeReexports", "rewriteThis", "rewriteLiveReferences", "hasStrict", "directives", "some", "value", "unshiftContainer", "headers", "hasExports", "push", "buildESModuleHeader", "nameList", "buildExportNameListDeclaration", "exportNameListName", "name", "statement", "buildExportInitializationStatements", "ensureStatementsHoisted", "statements", "for<PERSON>ach", "header", "_blockHoist", "wrapInterop", "programPath", "expr", "type", "hub", "addHelper", "helper", "Error", "buildNamespaceInitStatements", "metadata", "sourceMetadata", "srcNamespace", "localName", "importsNamespace", "template", "NAME", "SOURCE", "buildReexportsFromMeta", "reexportNamespace", "EXPORTS", "NAMESPACE", "reexportAll", "buildNamespaceReexport", "loc", "ReexportTemplate", "constant", "constantComputed", "spec", "namespace", "stringSpecifiers", "Array", "from", "reexports", "importName", "NAMESPACE_IMPORT", "interop", "has", "astNodes", "EXPORT_NAME", "VERIFY_NAME_LIST", "EXPORTS_LIST", "exportedVars", "Object", "create", "data", "local", "values", "names", "hasReexport", "source", "keys", "length", "scope", "generateUidIdentifier", "default", "initStatements", "kind", "buildInitStatement", "reexportsStatements", "i", "sort", "a", "b", "results", "initStatement", "chunkSize", "uninitializedExportNames", "j", "buildUndefinedNode", "InitTemplate", "computed", "expression", "exportNames", "initExpr", "reduce", "acc", "params", "VALUE"], "sources": ["../src/index.ts"], "sourcesContent": ["import assert from \"assert\";\nimport { template, types as t } from \"@babel/core\";\n\nimport { isModule } from \"@babel/helper-module-imports\";\n\nimport rewriteThis from \"./rewrite-this.ts\";\nimport rewriteLiveReferences from \"./rewrite-live-references.ts\";\nimport normalizeModuleAndLoadMetadata, {\n  hasExports,\n  isSideEffectImport,\n  validateImportInteropOption,\n} from \"./normalize-and-load-metadata.ts\";\nimport type {\n  ImportInterop,\n  InteropType,\n  Lazy,\n  ModuleMetadata,\n  SourceModuleMetadata,\n} from \"./normalize-and-load-metadata.ts\";\nimport type { NodePath } from \"@babel/traverse\";\n\nconst {\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  directive,\n  directiveLiteral,\n  expressionStatement,\n  identifier,\n  isIdentifier,\n  memberExpression,\n  stringLiteral,\n  valueToNode,\n  variableDeclaration,\n  variableDeclarator,\n} = t;\n\nexport { buildDynamicImport } from \"./dynamic-import.ts\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  if (!USE_ESM) {\n    if (!IS_STANDALONE) {\n      // eslint-disable-next-line no-restricted-globals\n      exports.getDynamicImportSource =\n        // eslint-disable-next-line no-restricted-globals, import/extensions\n        require(\"./dynamic-import\").getDynamicImportSource;\n    }\n  }\n}\n\nexport { default as getModuleName } from \"./get-module-name.ts\";\nexport type { PluginOptions } from \"./get-module-name.ts\";\n\nexport { hasExports, isSideEffectImport, isModule, rewriteThis };\n\nexport interface RewriteModuleStatementsAndPrepareHeaderOptions {\n  exportName?: string;\n  strict: boolean;\n  allowTopLevelThis?: boolean;\n  strictMode: boolean;\n  loose?: boolean;\n  importInterop?: ImportInterop;\n  noInterop?: boolean;\n  lazy?: Lazy;\n  esNamespaceOnly?: boolean;\n  filename: string | undefined;\n  constantReexports?: boolean | void;\n  enumerableModuleMeta?: boolean | void;\n  noIncompleteNsImportDetection?: boolean | void;\n}\n\n/**\n * Perform all of the generic ES6 module rewriting needed to handle initial\n * module processing. This function will rewrite the majority of the given\n * program to reference the modules described by the returned metadata,\n * and returns a list of statements for use when initializing the module.\n */\nexport function rewriteModuleStatementsAndPrepareHeader(\n  path: NodePath<t.Program>,\n  {\n    exportName,\n    strict,\n    allowTopLevelThis,\n    strictMode,\n    noInterop,\n    importInterop = noInterop ? \"none\" : \"babel\",\n    lazy,\n    esNamespaceOnly,\n    filename,\n\n    constantReexports = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    enumerableModuleMeta = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    noIncompleteNsImportDetection,\n  }: RewriteModuleStatementsAndPrepareHeaderOptions,\n) {\n  validateImportInteropOption(importInterop);\n  assert(isModule(path), \"Cannot process module statements in a script\");\n  path.node.sourceType = \"script\";\n\n  const meta = normalizeModuleAndLoadMetadata(path, exportName, {\n    importInterop,\n    initializeReexports: constantReexports,\n    lazy,\n    esNamespaceOnly,\n    filename,\n  });\n\n  if (!allowTopLevelThis) {\n    rewriteThis(path);\n  }\n\n  rewriteLiveReferences(path, meta);\n\n  if (strictMode !== false) {\n    const hasStrict = path.node.directives.some(directive => {\n      return directive.value.value === \"use strict\";\n    });\n    if (!hasStrict) {\n      path.unshiftContainer(\n        \"directives\",\n        directive(directiveLiteral(\"use strict\")),\n      );\n    }\n  }\n\n  const headers = [];\n  if (hasExports(meta) && !strict) {\n    headers.push(buildESModuleHeader(meta, enumerableModuleMeta));\n  }\n\n  const nameList = buildExportNameListDeclaration(path, meta);\n\n  if (nameList) {\n    meta.exportNameListName = nameList.name;\n    headers.push(nameList.statement);\n  }\n\n  // Create all of the statically known named exports.\n  headers.push(\n    ...buildExportInitializationStatements(\n      path,\n      meta,\n      constantReexports,\n      noIncompleteNsImportDetection,\n    ),\n  );\n\n  return { meta, headers };\n}\n\n/**\n * Flag a set of statements as hoisted above all else so that module init\n * statements all run before user code.\n */\nexport function ensureStatementsHoisted(statements: t.Statement[]) {\n  // Force all of the header fields to be at the top of the file.\n  statements.forEach(header => {\n    // @ts-expect-error Fixme: handle _blockHoist property\n    header._blockHoist = 3;\n  });\n}\n\n/**\n * Given an expression for a standard import object, like \"require('foo')\",\n * wrap it in a call to the interop helpers based on the type.\n */\nexport function wrapInterop(\n  programPath: NodePath,\n  expr: t.Expression,\n  type: InteropType,\n): t.CallExpression {\n  if (type === \"none\") {\n    return null;\n  }\n\n  if (type === \"node-namespace\") {\n    return callExpression(programPath.hub.addHelper(\"interopRequireWildcard\"), [\n      expr,\n      booleanLiteral(true),\n    ]);\n  } else if (type === \"node-default\") {\n    return null;\n  }\n\n  let helper;\n  if (type === \"default\") {\n    helper = \"interopRequireDefault\";\n  } else if (type === \"namespace\") {\n    helper = \"interopRequireWildcard\";\n  } else {\n    throw new Error(`Unknown interop: ${type}`);\n  }\n\n  return callExpression(programPath.hub.addHelper(helper), [expr]);\n}\n\n/**\n * Create the runtime initialization statements for a given requested source.\n * These will initialize all of the runtime import/export logic that\n * can't be handled statically by the statements created by\n * buildExportInitializationStatements().\n */\nexport function buildNamespaceInitStatements(\n  metadata: ModuleMetadata,\n  sourceMetadata: SourceModuleMetadata,\n  constantReexports: boolean | void = false,\n) {\n  const statements = [];\n\n  let srcNamespace: t.Node = identifier(sourceMetadata.name);\n  if (sourceMetadata.lazy) srcNamespace = callExpression(srcNamespace, []);\n\n  for (const localName of sourceMetadata.importsNamespace) {\n    if (localName === sourceMetadata.name) continue;\n\n    // Create and assign binding to namespace object\n    statements.push(\n      template.statement`var NAME = SOURCE;`({\n        NAME: localName,\n        SOURCE: cloneNode(srcNamespace),\n      }),\n    );\n  }\n  if (constantReexports) {\n    statements.push(...buildReexportsFromMeta(metadata, sourceMetadata, true));\n  }\n  for (const exportName of sourceMetadata.reexportNamespace) {\n    // Assign export to namespace object.\n    statements.push(\n      (sourceMetadata.lazy\n        ? template.statement`\n            Object.defineProperty(EXPORTS, \"NAME\", {\n              enumerable: true,\n              get: function() {\n                return NAMESPACE;\n              }\n            });\n          `\n        : template.statement`EXPORTS.NAME = NAMESPACE;`)({\n        EXPORTS: metadata.exportName,\n        NAME: exportName,\n        NAMESPACE: cloneNode(srcNamespace),\n      }),\n    );\n  }\n  if (sourceMetadata.reexportAll) {\n    const statement = buildNamespaceReexport(\n      metadata,\n      cloneNode(srcNamespace),\n      constantReexports,\n    );\n    statement.loc = sourceMetadata.reexportAll.loc;\n\n    // Iterate props creating getter for each prop.\n    statements.push(statement);\n  }\n  return statements;\n}\n\nconst ReexportTemplate = {\n  constant: template.statement`EXPORTS.EXPORT_NAME = NAMESPACE_IMPORT;`,\n  constantComputed: template.statement`EXPORTS[\"EXPORT_NAME\"] = NAMESPACE_IMPORT;`,\n  spec: template.statement`\n    Object.defineProperty(EXPORTS, \"EXPORT_NAME\", {\n      enumerable: true,\n      get: function() {\n        return NAMESPACE_IMPORT;\n      },\n    });\n    `,\n};\n\nfunction buildReexportsFromMeta(\n  meta: ModuleMetadata,\n  metadata: SourceModuleMetadata,\n  constantReexports: boolean,\n) {\n  const namespace = metadata.lazy\n    ? callExpression(identifier(metadata.name), [])\n    : identifier(metadata.name);\n\n  const { stringSpecifiers } = meta;\n  return Array.from(metadata.reexports, ([exportName, importName]) => {\n    let NAMESPACE_IMPORT: t.Expression = cloneNode(namespace);\n    if (importName === \"default\" && metadata.interop === \"node-default\") {\n      // Nothing, it's ok as-is\n    } else if (stringSpecifiers.has(importName)) {\n      NAMESPACE_IMPORT = memberExpression(\n        NAMESPACE_IMPORT,\n        stringLiteral(importName),\n        true,\n      );\n    } else {\n      NAMESPACE_IMPORT = memberExpression(\n        NAMESPACE_IMPORT,\n        identifier(importName),\n      );\n    }\n    const astNodes = {\n      EXPORTS: meta.exportName,\n      EXPORT_NAME: exportName,\n      NAMESPACE_IMPORT,\n    };\n    if (constantReexports || isIdentifier(NAMESPACE_IMPORT)) {\n      if (stringSpecifiers.has(exportName)) {\n        return ReexportTemplate.constantComputed(astNodes);\n      } else {\n        return ReexportTemplate.constant(astNodes);\n      }\n    } else {\n      return ReexportTemplate.spec(astNodes);\n    }\n  });\n}\n\n/**\n * Build an \"__esModule\" header statement setting the property on a given object.\n */\nfunction buildESModuleHeader(\n  metadata: ModuleMetadata,\n  enumerableModuleMeta: boolean | void = false,\n) {\n  return (\n    enumerableModuleMeta\n      ? template.statement`\n        EXPORTS.__esModule = true;\n      `\n      : template.statement`\n        Object.defineProperty(EXPORTS, \"__esModule\", {\n          value: true,\n        });\n      `\n  )({ EXPORTS: metadata.exportName });\n}\n\n/**\n * Create a re-export initialization loop for a specific imported namespace.\n */\nfunction buildNamespaceReexport(\n  metadata: ModuleMetadata,\n  namespace: t.Identifier | t.CallExpression,\n  constantReexports: boolean | void,\n) {\n  return (\n    constantReexports\n      ? template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          EXPORTS[key] = NAMESPACE[key];\n        });\n      `\n      : // Also skip already assigned bindings if they are strictly equal\n        // to be somewhat more spec-compliant when a file has multiple\n        // namespace re-exports that would cause a binding to be exported\n        // multiple times. However, multiple bindings of the same name that\n        // export the same primitive value are silently skipped\n        // (the spec requires an \"ambiguous bindings\" early error here).\n        template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          Object.defineProperty(EXPORTS, key, {\n            enumerable: true,\n            get: function() {\n              return NAMESPACE[key];\n            },\n          });\n        });\n    `\n  )({\n    NAMESPACE: namespace,\n    EXPORTS: metadata.exportName,\n    VERIFY_NAME_LIST: metadata.exportNameListName\n      ? template`\n            if (Object.prototype.hasOwnProperty.call(EXPORTS_LIST, key)) return;\n          `({ EXPORTS_LIST: metadata.exportNameListName })\n      : null,\n  });\n}\n\n/**\n * Build a statement declaring a variable that contains all of the exported\n * variable names in an object so they can easily be referenced from an\n * export * from statement to check for conflicts.\n */\nfunction buildExportNameListDeclaration(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n) {\n  const exportedVars = Object.create(null);\n  for (const data of metadata.local.values()) {\n    for (const name of data.names) {\n      exportedVars[name] = true;\n    }\n  }\n\n  let hasReexport = false;\n  for (const data of metadata.source.values()) {\n    for (const exportName of data.reexports.keys()) {\n      exportedVars[exportName] = true;\n    }\n    for (const exportName of data.reexportNamespace) {\n      exportedVars[exportName] = true;\n    }\n\n    hasReexport = hasReexport || !!data.reexportAll;\n  }\n\n  if (!hasReexport || Object.keys(exportedVars).length === 0) return null;\n\n  const name = programPath.scope.generateUidIdentifier(\"exportNames\");\n\n  delete exportedVars.default;\n\n  return {\n    name: name.name,\n    statement: variableDeclaration(\"var\", [\n      variableDeclarator(name, valueToNode(exportedVars)),\n    ]),\n  };\n}\n\n/**\n * Create a set of statements that will initialize all of the statically-known\n * export names with their expected values.\n */\nfunction buildExportInitializationStatements(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n  constantReexports: boolean | void = false,\n  noIncompleteNsImportDetection: boolean | void = false,\n) {\n  const initStatements: Array<[string, t.Statement | null]> = [];\n\n  for (const [localName, data] of metadata.local) {\n    if (data.kind === \"import\") {\n      // No-open since these are explicitly set with the \"reexports\" block.\n    } else if (data.kind === \"hoisted\") {\n      initStatements.push([\n        // data.names is always of length 1 because a hoisted export\n        // name must be id of a function declaration\n        data.names[0],\n        buildInitStatement(metadata, data.names, identifier(localName)),\n      ]);\n    } else if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.names) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  for (const data of metadata.source.values()) {\n    if (!constantReexports) {\n      const reexportsStatements = buildReexportsFromMeta(metadata, data, false);\n      const reexports = [...data.reexports.keys()];\n      for (let i = 0; i < reexportsStatements.length; i++) {\n        initStatements.push([reexports[i], reexportsStatements[i]]);\n      }\n    }\n    if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.reexportNamespace) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  // https://tc39.es/ecma262/#sec-module-namespace-exotic-objects\n  // The [Exports] list is ordered as if an Array of those String values\n  // had been sorted using %Array.prototype.sort% using undefined as comparefn\n  initStatements.sort(([a], [b]) => {\n    if (a < b) return -1;\n    if (b < a) return 1;\n    return 0;\n  });\n\n  const results = [];\n  if (noIncompleteNsImportDetection) {\n    for (const [, initStatement] of initStatements) {\n      results.push(initStatement);\n    }\n  } else {\n    // We generate init statements (`exports.a = exports.b = ... = void 0`)\n    // for every 100 exported names to avoid deeply-nested AST structures.\n    const chunkSize = 100;\n    for (let i = 0; i < initStatements.length; i += chunkSize) {\n      let uninitializedExportNames = [];\n      for (let j = 0; j < chunkSize && i + j < initStatements.length; j++) {\n        const [exportName, initStatement] = initStatements[i + j];\n        if (initStatement !== null) {\n          if (uninitializedExportNames.length > 0) {\n            results.push(\n              buildInitStatement(\n                metadata,\n                uninitializedExportNames,\n                programPath.scope.buildUndefinedNode(),\n              ),\n            );\n            // reset after uninitializedExportNames has been transformed\n            // to init statements\n            uninitializedExportNames = [];\n          }\n          results.push(initStatement);\n        } else {\n          uninitializedExportNames.push(exportName);\n        }\n      }\n      if (uninitializedExportNames.length > 0) {\n        results.push(\n          buildInitStatement(\n            metadata,\n            uninitializedExportNames,\n            programPath.scope.buildUndefinedNode(),\n          ),\n        );\n      }\n    }\n  }\n\n  return results;\n}\n\n/**\n * Given a set of export names, create a set of nested assignments to\n * initialize them all to a given expression.\n */\nconst InitTemplate = {\n  computed: template.expression`EXPORTS[\"NAME\"] = VALUE`,\n  default: template.expression`EXPORTS.NAME = VALUE`,\n};\n\nfunction buildInitStatement(\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  initExpr: t.Expression,\n) {\n  const { stringSpecifiers, exportName: EXPORTS } = metadata;\n  return expressionStatement(\n    exportNames.reduce((acc, exportName) => {\n      const params = {\n        EXPORTS,\n        NAME: exportName,\n        VALUE: acc,\n      };\n      if (stringSpecifiers.has(exportName)) {\n        return InitTemplate.computed(params);\n      } else {\n        return InitTemplate.default(params);\n      }\n    }, initExpr),\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,yBAAA,GAAAL,OAAA;AA8BA,IAAAM,cAAA,GAAAN,OAAA;AAaA,IAAAO,cAAA,GAAAP,OAAA;AA7BA,MAAM;EACJQ,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,SAAS;EACTC,gBAAgB;EAChBC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,gBAAgB;EAChBC,aAAa;EACbC,WAAW;EACXC,mBAAmB;EACnBC;AACF,CAAC,GAAGC,WAAC;AAI8B;EACnB;IACQ;MAElBC,OAAO,CAACC,sBAAsB,GAE5BvB,OAAO,CAAC,kBAAkB,CAAC,CAACuB,sBAAsB;IACtD;EACF;AACF;AA6BO,SAASC,uCAAuCA,CACrDC,IAAyB,EACzB;EACEC,UAAU;EACVC,MAAM;EACNC,iBAAiB;EACjBC,UAAU;EACVC,SAAS;EACTC,aAAa,GAAGD,SAAS,GAAG,MAAM,GAAG,OAAO;EAC5CE,IAAI;EACJC,eAAe;EACfC,QAAQ;EAERC,iBAAiB,GAEbC,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBC,oBAAoB,GAEhBF,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBE;AAC8C,CAAC,EACjD;EACA,IAAAC,qDAA2B,EAACT,aAAa,CAAC;EAC1CU,OAAM,CAAC,IAAAC,6BAAQ,EAACjB,IAAI,CAAC,EAAE,8CAA8C,CAAC;EACtEA,IAAI,CAACkB,IAAI,CAACC,UAAU,GAAG,QAAQ;EAE/B,MAAMC,IAAI,GAAG,IAAAC,iCAA8B,EAACrB,IAAI,EAAEC,UAAU,EAAE;IAC5DK,aAAa;IACbgB,mBAAmB,EAAEZ,iBAAiB;IACtCH,IAAI;IACJC,eAAe;IACfC;EACF,CAAC,CAAC;EAEF,IAAI,CAACN,iBAAiB,EAAE;IACtB,IAAAoB,oBAAW,EAACvB,IAAI,CAAC;EACnB;EAEA,IAAAwB,8BAAqB,EAACxB,IAAI,EAAEoB,IAAI,CAAC;EAEjC,IAAIhB,UAAU,KAAK,KAAK,EAAE;IACxB,MAAMqB,SAAS,GAAGzB,IAAI,CAACkB,IAAI,CAACQ,UAAU,CAACC,IAAI,CAACzC,SAAS,IAAI;MACvD,OAAOA,SAAS,CAAC0C,KAAK,CAACA,KAAK,KAAK,YAAY;IAC/C,CAAC,CAAC;IACF,IAAI,CAACH,SAAS,EAAE;MACdzB,IAAI,CAAC6B,gBAAgB,CACnB,YAAY,EACZ3C,SAAS,CAACC,gBAAgB,CAAC,YAAY,CAAC,CAC1C,CAAC;IACH;EACF;EAEA,MAAM2C,OAAO,GAAG,EAAE;EAClB,IAAI,IAAAC,oCAAU,EAACX,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;IAC/B4B,OAAO,CAACE,IAAI,CAACC,mBAAmB,CAACb,IAAI,EAAEP,oBAAoB,CAAC,CAAC;EAC/D;EAEA,MAAMqB,QAAQ,GAAGC,8BAA8B,CAACnC,IAAI,EAAEoB,IAAI,CAAC;EAE3D,IAAIc,QAAQ,EAAE;IACZd,IAAI,CAACgB,kBAAkB,GAAGF,QAAQ,CAACG,IAAI;IACvCP,OAAO,CAACE,IAAI,CAACE,QAAQ,CAACI,SAAS,CAAC;EAClC;EAGAR,OAAO,CAACE,IAAI,CACV,GAAGO,mCAAmC,CACpCvC,IAAI,EACJoB,IAAI,EACJV,iBAAiB,EACjBI,6BACF,CACF,CAAC;EAED,OAAO;IAAEM,IAAI;IAAEU;EAAQ,CAAC;AAC1B;AAMO,SAASU,uBAAuBA,CAACC,UAAyB,EAAE;EAEjEA,UAAU,CAACC,OAAO,CAACC,MAAM,IAAI;IAE3BA,MAAM,CAACC,WAAW,GAAG,CAAC;EACxB,CAAC,CAAC;AACJ;AAMO,SAASC,WAAWA,CACzBC,WAAqB,EACrBC,IAAkB,EAClBC,IAAiB,EACC;EAClB,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,KAAK,gBAAgB,EAAE;IAC7B,OAAOhE,cAAc,CAAC8D,WAAW,CAACG,GAAG,CAACC,SAAS,CAAC,wBAAwB,CAAC,EAAE,CACzEH,IAAI,EACJhE,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC;EACJ,CAAC,MAAM,IAAIiE,IAAI,KAAK,cAAc,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,IAAIG,MAAM;EACV,IAAIH,IAAI,KAAK,SAAS,EAAE;IACtBG,MAAM,GAAG,uBAAuB;EAClC,CAAC,MAAM,IAAIH,IAAI,KAAK,WAAW,EAAE;IAC/BG,MAAM,GAAG,wBAAwB;EACnC,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CAAE,oBAAmBJ,IAAK,EAAC,CAAC;EAC7C;EAEA,OAAOhE,cAAc,CAAC8D,WAAW,CAACG,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;AAClE;AAQO,SAASM,4BAA4BA,CAC1CC,QAAwB,EACxBC,cAAoC,EACpC7C,iBAAiC,GAAG,KAAK,EACzC;EACA,MAAM+B,UAAU,GAAG,EAAE;EAErB,IAAIe,YAAoB,GAAGnE,UAAU,CAACkE,cAAc,CAAClB,IAAI,CAAC;EAC1D,IAAIkB,cAAc,CAAChD,IAAI,EAAEiD,YAAY,GAAGxE,cAAc,CAACwE,YAAY,EAAE,EAAE,CAAC;EAExE,KAAK,MAAMC,SAAS,IAAIF,cAAc,CAACG,gBAAgB,EAAE;IACvD,IAAID,SAAS,KAAKF,cAAc,CAAClB,IAAI,EAAE;IAGvCI,UAAU,CAACT,IAAI,CACb2B,cAAQ,CAACrB,SAAU,oBAAmB,CAAC;MACrCsB,IAAI,EAAEH,SAAS;MACfI,MAAM,EAAE5E,SAAS,CAACuE,YAAY;IAChC,CAAC,CACH,CAAC;EACH;EACA,IAAI9C,iBAAiB,EAAE;IACrB+B,UAAU,CAACT,IAAI,CAAC,GAAG8B,sBAAsB,CAACR,QAAQ,EAAEC,cAAc,EAAE,IAAI,CAAC,CAAC;EAC5E;EACA,KAAK,MAAMtD,UAAU,IAAIsD,cAAc,CAACQ,iBAAiB,EAAE;IAEzDtB,UAAU,CAACT,IAAI,CACb,CAACuB,cAAc,CAAChD,IAAI,GAChBoD,cAAQ,CAACrB,SAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GACDqB,cAAQ,CAACrB,SAAU,2BAA0B,EAAE;MACjD0B,OAAO,EAAEV,QAAQ,CAACrD,UAAU;MAC5B2D,IAAI,EAAE3D,UAAU;MAChBgE,SAAS,EAAEhF,SAAS,CAACuE,YAAY;IACnC,CAAC,CACH,CAAC;EACH;EACA,IAAID,cAAc,CAACW,WAAW,EAAE;IAC9B,MAAM5B,SAAS,GAAG6B,sBAAsB,CACtCb,QAAQ,EACRrE,SAAS,CAACuE,YAAY,CAAC,EACvB9C,iBACF,CAAC;IACD4B,SAAS,CAAC8B,GAAG,GAAGb,cAAc,CAACW,WAAW,CAACE,GAAG;IAG9C3B,UAAU,CAACT,IAAI,CAACM,SAAS,CAAC;EAC5B;EACA,OAAOG,UAAU;AACnB;AAEA,MAAM4B,gBAAgB,GAAG;EACvBC,QAAQ,EAAEX,cAAQ,CAACrB,SAAU,yCAAwC;EACrEiC,gBAAgB,EAAEZ,cAAQ,CAACrB,SAAU,4CAA2C;EAChFkC,IAAI,EAAEb,cAAQ,CAACrB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,SAASwB,sBAAsBA,CAC7B1C,IAAoB,EACpBkC,QAA8B,EAC9B5C,iBAA0B,EAC1B;EACA,MAAM+D,SAAS,GAAGnB,QAAQ,CAAC/C,IAAI,GAC3BvB,cAAc,CAACK,UAAU,CAACiE,QAAQ,CAACjB,IAAI,CAAC,EAAE,EAAE,CAAC,GAC7ChD,UAAU,CAACiE,QAAQ,CAACjB,IAAI,CAAC;EAE7B,MAAM;IAAEqC;EAAiB,CAAC,GAAGtD,IAAI;EACjC,OAAOuD,KAAK,CAACC,IAAI,CAACtB,QAAQ,CAACuB,SAAS,EAAE,CAAC,CAAC5E,UAAU,EAAE6E,UAAU,CAAC,KAAK;IAClE,IAAIC,gBAA8B,GAAG9F,SAAS,CAACwF,SAAS,CAAC;IACzD,IAAIK,UAAU,KAAK,SAAS,IAAIxB,QAAQ,CAAC0B,OAAO,KAAK,cAAc,EAAE,CAErE,CAAC,MAAM,IAAIN,gBAAgB,CAACO,GAAG,CAACH,UAAU,CAAC,EAAE;MAC3CC,gBAAgB,GAAGxF,gBAAgB,CACjCwF,gBAAgB,EAChBvF,aAAa,CAACsF,UAAU,CAAC,EACzB,IACF,CAAC;IACH,CAAC,MAAM;MACLC,gBAAgB,GAAGxF,gBAAgB,CACjCwF,gBAAgB,EAChB1F,UAAU,CAACyF,UAAU,CACvB,CAAC;IACH;IACA,MAAMI,QAAQ,GAAG;MACflB,OAAO,EAAE5C,IAAI,CAACnB,UAAU;MACxBkF,WAAW,EAAElF,UAAU;MACvB8E;IACF,CAAC;IACD,IAAIrE,iBAAiB,IAAIpB,YAAY,CAACyF,gBAAgB,CAAC,EAAE;MACvD,IAAIL,gBAAgB,CAACO,GAAG,CAAChF,UAAU,CAAC,EAAE;QACpC,OAAOoE,gBAAgB,CAACE,gBAAgB,CAACW,QAAQ,CAAC;MACpD,CAAC,MAAM;QACL,OAAOb,gBAAgB,CAACC,QAAQ,CAACY,QAAQ,CAAC;MAC5C;IACF,CAAC,MAAM;MACL,OAAOb,gBAAgB,CAACG,IAAI,CAACU,QAAQ,CAAC;IACxC;EACF,CAAC,CAAC;AACJ;AAKA,SAASjD,mBAAmBA,CAC1BqB,QAAwB,EACxBzC,oBAAoC,GAAG,KAAK,EAC5C;EACA,OAAO,CACLA,oBAAoB,GAChB8C,cAAQ,CAACrB,SAAU;AAC3B;AACA,OAAO,GACCqB,cAAQ,CAACrB,SAAU;AAC3B;AACA;AACA;AACA,OAAO,EACH;IAAE0B,OAAO,EAAEV,QAAQ,CAACrD;EAAW,CAAC,CAAC;AACrC;AAKA,SAASkE,sBAAsBA,CAC7Bb,QAAwB,EACxBmB,SAA0C,EAC1C/D,iBAAiC,EACjC;EACA,OAAO,CACLA,iBAAiB,GACbiD,cAAQ,CAACrB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAOCqB,cAAQ,CAACrB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EACD;IACA2B,SAAS,EAAEQ,SAAS;IACpBT,OAAO,EAAEV,QAAQ,CAACrD,UAAU;IAC5BmF,gBAAgB,EAAE9B,QAAQ,CAAClB,kBAAkB,GACzC,IAAAuB,cAAQ,CAAC;AACjB;AACA,WAAW,CAAC;MAAE0B,YAAY,EAAE/B,QAAQ,CAAClB;IAAmB,CAAC,CAAC,GAClD;EACN,CAAC,CAAC;AACJ;AAOA,SAASD,8BAA8BA,CACrCW,WAAqB,EACrBQ,QAAwB,EACxB;EACA,MAAMgC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxC,KAAK,MAAMC,IAAI,IAAInC,QAAQ,CAACoC,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE;IAC1C,KAAK,MAAMtD,IAAI,IAAIoD,IAAI,CAACG,KAAK,EAAE;MAC7BN,YAAY,CAACjD,IAAI,CAAC,GAAG,IAAI;IAC3B;EACF;EAEA,IAAIwD,WAAW,GAAG,KAAK;EACvB,KAAK,MAAMJ,IAAI,IAAInC,QAAQ,CAACwC,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,KAAK,MAAM1F,UAAU,IAAIwF,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC9CT,YAAY,CAACrF,UAAU,CAAC,GAAG,IAAI;IACjC;IACA,KAAK,MAAMA,UAAU,IAAIwF,IAAI,CAAC1B,iBAAiB,EAAE;MAC/CuB,YAAY,CAACrF,UAAU,CAAC,GAAG,IAAI;IACjC;IAEA4F,WAAW,GAAGA,WAAW,IAAI,CAAC,CAACJ,IAAI,CAACvB,WAAW;EACjD;EAEA,IAAI,CAAC2B,WAAW,IAAIN,MAAM,CAACQ,IAAI,CAACT,YAAY,CAAC,CAACU,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEvE,MAAM3D,IAAI,GAAGS,WAAW,CAACmD,KAAK,CAACC,qBAAqB,CAAC,aAAa,CAAC;EAEnE,OAAOZ,YAAY,CAACa,OAAO;EAE3B,OAAO;IACL9D,IAAI,EAAEA,IAAI,CAACA,IAAI;IACfC,SAAS,EAAE5C,mBAAmB,CAAC,KAAK,EAAE,CACpCC,kBAAkB,CAAC0C,IAAI,EAAE5C,WAAW,CAAC6F,YAAY,CAAC,CAAC,CACpD;EACH,CAAC;AACH;AAMA,SAAS/C,mCAAmCA,CAC1CO,WAAqB,EACrBQ,QAAwB,EACxB5C,iBAAiC,GAAG,KAAK,EACzCI,6BAA6C,GAAG,KAAK,EACrD;EACA,MAAMsF,cAAmD,GAAG,EAAE;EAE9D,KAAK,MAAM,CAAC3C,SAAS,EAAEgC,IAAI,CAAC,IAAInC,QAAQ,CAACoC,KAAK,EAAE;IAC9C,IAAID,IAAI,CAACY,IAAI,KAAK,QAAQ,EAAE,CAE5B,CAAC,MAAM,IAAIZ,IAAI,CAACY,IAAI,KAAK,SAAS,EAAE;MAClCD,cAAc,CAACpE,IAAI,CAAC,CAGlByD,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,EACbU,kBAAkB,CAAChD,QAAQ,EAAEmC,IAAI,CAACG,KAAK,EAAEvG,UAAU,CAACoE,SAAS,CAAC,CAAC,CAChE,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC3C,6BAA6B,EAAE;MACzC,KAAK,MAAMb,UAAU,IAAIwF,IAAI,CAACG,KAAK,EAAE;QACnCQ,cAAc,CAACpE,IAAI,CAAC,CAAC/B,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAEA,KAAK,MAAMwF,IAAI,IAAInC,QAAQ,CAACwC,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,IAAI,CAACjF,iBAAiB,EAAE;MACtB,MAAM6F,mBAAmB,GAAGzC,sBAAsB,CAACR,QAAQ,EAAEmC,IAAI,EAAE,KAAK,CAAC;MACzE,MAAMZ,SAAS,GAAG,CAAC,GAAGY,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,CAAC;MAC5C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,mBAAmB,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;QACnDJ,cAAc,CAACpE,IAAI,CAAC,CAAC6C,SAAS,CAAC2B,CAAC,CAAC,EAAED,mBAAmB,CAACC,CAAC,CAAC,CAAC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC1F,6BAA6B,EAAE;MAClC,KAAK,MAAMb,UAAU,IAAIwF,IAAI,CAAC1B,iBAAiB,EAAE;QAC/CqC,cAAc,CAACpE,IAAI,CAAC,CAAC/B,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAKAmG,cAAc,CAACK,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAK;IAChC,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;IACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;IACnB,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,MAAME,OAAO,GAAG,EAAE;EAClB,IAAI9F,6BAA6B,EAAE;IACjC,KAAK,MAAM,GAAG+F,aAAa,CAAC,IAAIT,cAAc,EAAE;MAC9CQ,OAAO,CAAC5E,IAAI,CAAC6E,aAAa,CAAC;IAC7B;EACF,CAAC,MAAM;IAGL,MAAMC,SAAS,GAAG,GAAG;IACrB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACJ,MAAM,EAAEQ,CAAC,IAAIM,SAAS,EAAE;MACzD,IAAIC,wBAAwB,GAAG,EAAE;MACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,IAAIN,CAAC,GAAGQ,CAAC,GAAGZ,cAAc,CAACJ,MAAM,EAAEgB,CAAC,EAAE,EAAE;QACnE,MAAM,CAAC/G,UAAU,EAAE4G,aAAa,CAAC,GAAGT,cAAc,CAACI,CAAC,GAAGQ,CAAC,CAAC;QACzD,IAAIH,aAAa,KAAK,IAAI,EAAE;UAC1B,IAAIE,wBAAwB,CAACf,MAAM,GAAG,CAAC,EAAE;YACvCY,OAAO,CAAC5E,IAAI,CACVsE,kBAAkB,CAChBhD,QAAQ,EACRyD,wBAAwB,EACxBjE,WAAW,CAACmD,KAAK,CAACgB,kBAAkB,CAAC,CACvC,CACF,CAAC;YAGDF,wBAAwB,GAAG,EAAE;UAC/B;UACAH,OAAO,CAAC5E,IAAI,CAAC6E,aAAa,CAAC;QAC7B,CAAC,MAAM;UACLE,wBAAwB,CAAC/E,IAAI,CAAC/B,UAAU,CAAC;QAC3C;MACF;MACA,IAAI8G,wBAAwB,CAACf,MAAM,GAAG,CAAC,EAAE;QACvCY,OAAO,CAAC5E,IAAI,CACVsE,kBAAkB,CAChBhD,QAAQ,EACRyD,wBAAwB,EACxBjE,WAAW,CAACmD,KAAK,CAACgB,kBAAkB,CAAC,CACvC,CACF,CAAC;MACH;IACF;EACF;EAEA,OAAOL,OAAO;AAChB;AAMA,MAAMM,YAAY,GAAG;EACnBC,QAAQ,EAAExD,cAAQ,CAACyD,UAAW,yBAAwB;EACtDjB,OAAO,EAAExC,cAAQ,CAACyD,UAAW;AAC/B,CAAC;AAED,SAASd,kBAAkBA,CACzBhD,QAAwB,EACxB+D,WAAqB,EACrBC,QAAsB,EACtB;EACA,MAAM;IAAE5C,gBAAgB;IAAEzE,UAAU,EAAE+D;EAAQ,CAAC,GAAGV,QAAQ;EAC1D,OAAOlE,mBAAmB,CACxBiI,WAAW,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEvH,UAAU,KAAK;IACtC,MAAMwH,MAAM,GAAG;MACbzD,OAAO;MACPJ,IAAI,EAAE3D,UAAU;MAChByH,KAAK,EAAEF;IACT,CAAC;IACD,IAAI9C,gBAAgB,CAACO,GAAG,CAAChF,UAAU,CAAC,EAAE;MACpC,OAAOiH,YAAY,CAACC,QAAQ,CAACM,MAAM,CAAC;IACtC,CAAC,MAAM;MACL,OAAOP,YAAY,CAACf,OAAO,CAACsB,MAAM,CAAC;IACrC;EACF,CAAC,EAAEH,QAAQ,CACb,CAAC;AACH"}