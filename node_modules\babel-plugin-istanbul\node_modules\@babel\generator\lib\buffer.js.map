{"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "map", "_map", "_buf", "_str", "_appendCount", "_last", "_queue", "_queueCursor", "_canMarkIdName", "_position", "line", "column", "_sourcePosition", "identifierName", "undefined", "identifierNamePos", "filename", "_allocQueue", "queue", "i", "push", "char", "repeat", "_pushQueue", "cursor", "length", "item", "_popQueue", "Error", "get", "_flush", "result", "code", "trimRight", "decodedMap", "getDecoded", "__mergedMap", "resultMap", "value", "Object", "defineProperty", "writable", "rawMappings", "mappings", "getRawMappings", "append", "str", "maybeNewline", "_append", "appendChar", "_appendChar", "sourcePosition", "queueIndentation", "queueCursor", "sourcePos", "String", "fromCharCode", "_mark", "len", "position", "charCodeAt", "indexOf", "last", "_this$_map", "mark", "removeTrailingNewline", "removeLastSemicolon", "getLastChar", "getNewlineCount", "count", "endsWithCharAndNewline", "lastCp", "<PERSON><PERSON><PERSON><PERSON>", "exactSource", "loc", "cb", "source", "prop", "_normalizePosition", "sourceWithOffset", "columnOffset", "withSource", "pos", "target", "Math", "max", "getCurrentColumn", "lastIndex", "getCurrentLine", "exports", "default"], "sources": ["../src/buffer.ts"], "sourcesContent": ["import type SourceMap from \"./source-map.ts\";\nimport * as charcodes from \"charcodes\";\n\nexport type Pos = {\n  line: number;\n  column: number;\n};\nexport type Loc = {\n  start?: Pos;\n  end?: Pos;\n  filename?: string;\n};\ntype SourcePos = {\n  line: number | undefined;\n  column: number | undefined;\n  identifierName: string | undefined;\n  filename: string | undefined;\n};\ntype InternalSourcePos = SourcePos & { identifierNamePos: Pos };\n\ntype QueueItem = {\n  char: number;\n  repeat: number;\n  line: number | undefined;\n  column: number | undefined;\n  identifierName: undefined; // Not used, it always undefined.\n  identifierNamePos: undefined; // Not used, it always undefined.\n  filename: string | undefined;\n};\n\nexport default class Buffer {\n  constructor(map?: SourceMap | null) {\n    this._map = map;\n\n    this._allocQueue();\n  }\n\n  _map: SourceMap = null;\n  _buf = \"\";\n  _str = \"\";\n  _appendCount = 0;\n  _last = 0;\n  _queue: QueueItem[] = [];\n  _queueCursor = 0;\n  _canMarkIdName = true;\n\n  _position = {\n    line: 1,\n    column: 0,\n  };\n  _sourcePosition: InternalSourcePos = {\n    identifierName: undefined,\n    identifierNamePos: undefined,\n    line: undefined,\n    column: undefined,\n    filename: undefined,\n  };\n\n  _allocQueue() {\n    const queue = this._queue;\n\n    for (let i = 0; i < 16; i++) {\n      queue.push({\n        char: 0,\n        repeat: 1,\n        line: undefined,\n        column: undefined,\n        identifierName: undefined,\n        identifierNamePos: undefined,\n        filename: \"\",\n      });\n    }\n  }\n\n  _pushQueue(\n    char: number,\n    repeat: number,\n    line: number | undefined,\n    column: number | undefined,\n    filename: string | undefined,\n  ) {\n    const cursor = this._queueCursor;\n    if (cursor === this._queue.length) {\n      this._allocQueue();\n    }\n    const item = this._queue[cursor];\n    item.char = char;\n    item.repeat = repeat;\n    item.line = line;\n    item.column = column;\n    item.filename = filename;\n\n    this._queueCursor++;\n  }\n\n  _popQueue(): QueueItem {\n    if (this._queueCursor === 0) {\n      throw new Error(\"Cannot pop from empty queue\");\n    }\n    return this._queue[--this._queueCursor];\n  }\n\n  /**\n   * Get the final string output from the buffer, along with the sourcemap if one exists.\n   */\n\n  get() {\n    this._flush();\n\n    const map = this._map;\n    const result = {\n      // Whatever trim is used here should not execute a regex against the\n      // source string since it may be arbitrarily large after all transformations\n      code: (this._buf + this._str).trimRight(),\n      // Decoded sourcemap is free to generate.\n      decodedMap: map?.getDecoded(),\n      // Used as a marker for backwards compatibility. We moved input map merging\n      // into the generator. We cannot merge the input map a second time, so the\n      // presence of this field tells us we've already done the work.\n      get __mergedMap() {\n        return this.map;\n      },\n      // Encoding the sourcemap is moderately CPU expensive.\n      get map() {\n        const resultMap = map ? map.get() : null;\n        result.map = resultMap;\n        return resultMap;\n      },\n      set map(value) {\n        Object.defineProperty(result, \"map\", { value, writable: true });\n      },\n      // Retrieving the raw mappings is very memory intensive.\n      get rawMappings() {\n        const mappings = map?.getRawMappings();\n        result.rawMappings = mappings;\n        return mappings;\n      },\n      set rawMappings(value) {\n        Object.defineProperty(result, \"rawMappings\", { value, writable: true });\n      },\n    };\n\n    return result;\n  }\n\n  /**\n   * Add a string to the buffer that cannot be reverted.\n   */\n\n  append(str: string, maybeNewline: boolean): void {\n    this._flush();\n\n    this._append(str, this._sourcePosition, maybeNewline);\n  }\n\n  appendChar(char: number): void {\n    this._flush();\n    this._appendChar(char, 1, this._sourcePosition);\n  }\n\n  /**\n   * Add a string to the buffer than can be reverted.\n   */\n  queue(char: number): void {\n    // Drop trailing spaces when a newline is inserted.\n    if (char === charcodes.lineFeed) {\n      while (this._queueCursor !== 0) {\n        const char = this._queue[this._queueCursor - 1].char;\n        if (char !== charcodes.space && char !== charcodes.tab) {\n          break;\n        }\n\n        this._queueCursor--;\n      }\n    }\n\n    const sourcePosition = this._sourcePosition;\n    this._pushQueue(\n      char,\n      1,\n      sourcePosition.line,\n      sourcePosition.column,\n      sourcePosition.filename,\n    );\n  }\n\n  /**\n   * Same as queue, but this indentation will never have a sourcemap marker.\n   */\n  queueIndentation(char: number, repeat: number): void {\n    this._pushQueue(char, repeat, undefined, undefined, undefined);\n  }\n\n  _flush(): void {\n    const queueCursor = this._queueCursor;\n    const queue = this._queue;\n    for (let i = 0; i < queueCursor; i++) {\n      const item: QueueItem = queue[i];\n      this._appendChar(item.char, item.repeat, item);\n    }\n    this._queueCursor = 0;\n  }\n\n  _appendChar(\n    char: number,\n    repeat: number,\n    sourcePos: InternalSourcePos,\n  ): void {\n    this._last = char;\n\n    this._str +=\n      repeat > 1\n        ? String.fromCharCode(char).repeat(repeat)\n        : String.fromCharCode(char);\n\n    if (char !== charcodes.lineFeed) {\n      this._mark(\n        sourcePos.line,\n        sourcePos.column,\n        sourcePos.identifierName,\n        sourcePos.identifierNamePos,\n        sourcePos.filename,\n      );\n      this._position.column += repeat;\n    } else {\n      this._position.line++;\n      this._position.column = 0;\n    }\n\n    if (this._canMarkIdName) {\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n  }\n\n  _append(\n    str: string,\n    sourcePos: InternalSourcePos,\n    maybeNewline: boolean,\n  ): void {\n    const len = str.length;\n    const position = this._position;\n\n    this._last = str.charCodeAt(len - 1);\n\n    if (++this._appendCount > 4096) {\n      +this._str; // Unexplainable huge performance boost. Ref: https://github.com/davidmarkclements/flatstr License: MIT\n      this._buf += this._str;\n      this._str = str;\n      this._appendCount = 0;\n    } else {\n      this._str += str;\n    }\n\n    if (!maybeNewline && !this._map) {\n      position.column += len;\n      return;\n    }\n\n    const { column, identifierName, identifierNamePos, filename } = sourcePos;\n    let line = sourcePos.line;\n\n    if (\n      (identifierName != null || identifierNamePos != null) &&\n      this._canMarkIdName\n    ) {\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n\n    // Search for newline chars. We search only for `\\n`, since both `\\r` and\n    // `\\r\\n` are normalized to `\\n` during parse. We exclude `\\u2028` and\n    // `\\u2029` for performance reasons, they're so uncommon that it's probably\n    // ok. It's also unclear how other sourcemap utilities handle them...\n    let i = str.indexOf(\"\\n\");\n    let last = 0;\n\n    // If the string starts with a newline char, then adding a mark is redundant.\n    // This catches both \"no newlines\" and \"newline after several chars\".\n    if (i !== 0) {\n      this._mark(line, column, identifierName, identifierNamePos, filename);\n    }\n\n    // Now, find each remaining newline char in the string.\n    while (i !== -1) {\n      position.line++;\n      position.column = 0;\n      last = i + 1;\n\n      // We mark the start of each line, which happens directly after this newline char\n      // unless this is the last char.\n      // When manually adding multi-line content (such as a comment), `line` will be `undefined`.\n      if (last < len && line !== undefined) {\n        this._mark(++line, 0, null, null, filename);\n      }\n      i = str.indexOf(\"\\n\", last);\n    }\n    position.column += len - last;\n  }\n\n  _mark(\n    line: number | undefined,\n    column: number | undefined,\n    identifierName: string | undefined,\n    identifierNamePos: Pos | undefined,\n    filename: string | undefined,\n  ): void {\n    this._map?.mark(\n      this._position,\n      line,\n      column,\n      identifierName,\n      identifierNamePos,\n      filename,\n    );\n  }\n\n  removeTrailingNewline(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.lineFeed\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  removeLastSemicolon(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.semicolon\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  getLastChar(): number {\n    const queueCursor = this._queueCursor;\n    return queueCursor !== 0 ? this._queue[queueCursor - 1].char : this._last;\n  }\n\n  /**\n   * This will only detect at most 1 newline after a call to `flush()`,\n   * but this has not been found so far, and an accurate count can be achieved if needed later.\n   */\n  getNewlineCount(): number {\n    const queueCursor = this._queueCursor;\n    let count = 0;\n    if (queueCursor === 0) return this._last === charcodes.lineFeed ? 1 : 0;\n    for (let i = queueCursor - 1; i >= 0; i--) {\n      if (this._queue[i].char !== charcodes.lineFeed) {\n        break;\n      }\n      count++;\n    }\n    return count === queueCursor && this._last === charcodes.lineFeed\n      ? count + 1\n      : count;\n  }\n\n  /**\n   * check if current _last + queue ends with newline, return the character before newline\n   *\n   * @param {*} ch\n   * @memberof Buffer\n   */\n  endsWithCharAndNewline(): number {\n    const queue = this._queue;\n    const queueCursor = this._queueCursor;\n    if (queueCursor !== 0) {\n      // every element in queue is one-length whitespace string\n      const lastCp = queue[queueCursor - 1].char;\n      if (lastCp !== charcodes.lineFeed) return;\n      if (queueCursor > 1) {\n        return queue[queueCursor - 2].char;\n      } else {\n        return this._last;\n      }\n    }\n    // We assume that everything being matched is at most a single token plus some whitespace,\n    // which everything currently is, but otherwise we'd have to expand _last or check _buf.\n  }\n\n  hasContent(): boolean {\n    return this._queueCursor !== 0 || !!this._last;\n  }\n\n  /**\n   * Certain sourcemap usecases expect mappings to be more accurate than\n   * Babel's generic sourcemap handling allows. For now, we special-case\n   * identifiers to allow for the primary cases to work.\n   * The goal of this line is to ensure that the map output from Babel will\n   * have an exact range on identifiers in the output code. Without this\n   * line, Babel would potentially include some number of trailing tokens\n   * that are printed after the identifier, but before another location has\n   * been assigned.\n   * This allows tooling like Rollup and Webpack to more accurately perform\n   * their own transformations. Most importantly, this allows the import/export\n   * transformations performed by those tools to loose less information when\n   * applying their own transformations on top of the code and map results\n   * generated by Babel itself.\n   *\n   * The primary example of this is the snippet:\n   *\n   *   import mod from \"mod\";\n   *   mod();\n   *\n   * With this line, there will be one mapping range over \"mod\" and another\n   * over \"();\", where previously it would have been a single mapping.\n   */\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!this._map) {\n      cb();\n      return;\n    }\n\n    this.source(\"start\", loc);\n    // @ts-expect-error identifierName is not defined\n    const identifierName = loc.identifierName;\n    const sourcePos = this._sourcePosition;\n    if (identifierName) {\n      this._canMarkIdName = false;\n      sourcePos.identifierName = identifierName;\n    }\n    cb();\n\n    if (identifierName) {\n      this._canMarkIdName = true;\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n    this.source(\"end\", loc);\n  }\n\n  /**\n   * Sets a given position as the current source location so generated code after this call\n   * will be given this position in the sourcemap.\n   */\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!this._map) return;\n\n    // Since this is called extremely often, we reuse the same _sourcePosition\n    // object for the whole lifetime of the buffer.\n    this._normalizePosition(prop, loc, 0);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!this._map) return;\n\n    this._normalizePosition(prop, loc, columnOffset);\n  }\n\n  /**\n   * Call a callback with a specific source location\n   */\n\n  withSource(prop: \"start\" | \"end\", loc: Loc, cb: () => void): void {\n    if (this._map) {\n      this.source(prop, loc);\n    }\n\n    cb();\n  }\n\n  _normalizePosition(prop: \"start\" | \"end\", loc: Loc, columnOffset: number) {\n    const pos = loc[prop];\n    const target = this._sourcePosition;\n\n    if (pos) {\n      target.line = pos.line;\n      // TODO: Fix https://github.com/babel/babel/issues/15712 in downstream\n      target.column = Math.max(pos.column + columnOffset, 0);\n      target.filename = loc.filename;\n    }\n  }\n\n  getCurrentColumn(): number {\n    const queue = this._queue;\n    const queueCursor = this._queueCursor;\n\n    let lastIndex = -1;\n    let len = 0;\n    for (let i = 0; i < queueCursor; i++) {\n      const item = queue[i];\n      if (item.char === charcodes.lineFeed) {\n        lastIndex = len;\n      }\n      len += item.repeat;\n    }\n\n    return lastIndex === -1 ? this._position.column + len : len - 1 - lastIndex;\n  }\n\n  getCurrentLine(): number {\n    let count = 0;\n\n    const queue = this._queue;\n    for (let i = 0; i < this._queueCursor; i++) {\n      if (queue[i].char === charcodes.lineFeed) {\n        count++;\n      }\n    }\n\n    return this._position.line + count;\n  }\n}\n"], "mappings": ";;;;;;AA8Be,MAAMA,MAAM,CAAC;EAC1BC,WAAWA,CAACC,GAAsB,EAAE;IAAA,KAMpCC,IAAI,GAAc,IAAI;IAAA,KACtBC,IAAI,GAAG,EAAE;IAAA,KACTC,IAAI,GAAG,EAAE;IAAA,KACTC,YAAY,GAAG,CAAC;IAAA,KAChBC,KAAK,GAAG,CAAC;IAAA,KACTC,MAAM,GAAgB,EAAE;IAAA,KACxBC,YAAY,GAAG,CAAC;IAAA,KAChBC,cAAc,GAAG,IAAI;IAAA,KAErBC,SAAS,GAAG;MACVC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE;IACV,CAAC;IAAA,KACDC,eAAe,GAAsB;MACnCC,cAAc,EAAEC,SAAS;MACzBC,iBAAiB,EAAED,SAAS;MAC5BJ,IAAI,EAAEI,SAAS;MACfH,MAAM,EAAEG,SAAS;MACjBE,QAAQ,EAAEF;IACZ,CAAC;IAxBC,IAAI,CAACb,IAAI,GAAGD,GAAG;IAEf,IAAI,CAACiB,WAAW,CAAC,CAAC;EACpB;EAuBAA,WAAWA,CAAA,EAAG;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACZ,MAAM;IAEzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BD,KAAK,CAACE,IAAI,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE,CAAC;QACTZ,IAAI,EAAEI,SAAS;QACfH,MAAM,EAAEG,SAAS;QACjBD,cAAc,EAAEC,SAAS;QACzBC,iBAAiB,EAAED,SAAS;QAC5BE,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF;EAEAO,UAAUA,CACRF,IAAY,EACZC,MAAc,EACdZ,IAAwB,EACxBC,MAA0B,EAC1BK,QAA4B,EAC5B;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACjB,YAAY;IAChC,IAAIiB,MAAM,KAAK,IAAI,CAAClB,MAAM,CAACmB,MAAM,EAAE;MACjC,IAAI,CAACR,WAAW,CAAC,CAAC;IACpB;IACA,MAAMS,IAAI,GAAG,IAAI,CAACpB,MAAM,CAACkB,MAAM,CAAC;IAChCE,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChBK,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpBI,IAAI,CAAChB,IAAI,GAAGA,IAAI;IAChBgB,IAAI,CAACf,MAAM,GAAGA,MAAM;IACpBe,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACT,YAAY,EAAE;EACrB;EAEAoB,SAASA,CAAA,EAAc;IACrB,IAAI,IAAI,CAACpB,YAAY,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIqB,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,OAAO,IAAI,CAACtB,MAAM,CAAC,EAAE,IAAI,CAACC,YAAY,CAAC;EACzC;EAMAsB,GAAGA,CAAA,EAAG;IACJ,IAAI,CAACC,MAAM,CAAC,CAAC;IAEb,MAAM9B,GAAG,GAAG,IAAI,CAACC,IAAI;IACrB,MAAM8B,MAAM,GAAG;MAGbC,IAAI,EAAE,CAAC,IAAI,CAAC9B,IAAI,GAAG,IAAI,CAACC,IAAI,EAAE8B,SAAS,CAAC,CAAC;MAEzCC,UAAU,EAAElC,GAAG,oBAAHA,GAAG,CAAEmC,UAAU,CAAC,CAAC;MAI7B,IAAIC,WAAWA,CAAA,EAAG;QAChB,OAAO,IAAI,CAACpC,GAAG;MACjB,CAAC;MAED,IAAIA,GAAGA,CAAA,EAAG;QACR,MAAMqC,SAAS,GAAGrC,GAAG,GAAGA,GAAG,CAAC6B,GAAG,CAAC,CAAC,GAAG,IAAI;QACxCE,MAAM,CAAC/B,GAAG,GAAGqC,SAAS;QACtB,OAAOA,SAAS;MAClB,CAAC;MACD,IAAIrC,GAAGA,CAACsC,KAAK,EAAE;QACbC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAE,KAAK,EAAE;UAAEO,KAAK;UAAEG,QAAQ,EAAE;QAAK,CAAC,CAAC;MACjE,CAAC;MAED,IAAIC,WAAWA,CAAA,EAAG;QAChB,MAAMC,QAAQ,GAAG3C,GAAG,oBAAHA,GAAG,CAAE4C,cAAc,CAAC,CAAC;QACtCb,MAAM,CAACW,WAAW,GAAGC,QAAQ;QAC7B,OAAOA,QAAQ;MACjB,CAAC;MACD,IAAID,WAAWA,CAACJ,KAAK,EAAE;QACrBC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAE,aAAa,EAAE;UAAEO,KAAK;UAAEG,QAAQ,EAAE;QAAK,CAAC,CAAC;MACzE;IACF,CAAC;IAED,OAAOV,MAAM;EACf;EAMAc,MAAMA,CAACC,GAAW,EAAEC,YAAqB,EAAQ;IAC/C,IAAI,CAACjB,MAAM,CAAC,CAAC;IAEb,IAAI,CAACkB,OAAO,CAACF,GAAG,EAAE,IAAI,CAAClC,eAAe,EAAEmC,YAAY,CAAC;EACvD;EAEAE,UAAUA,CAAC5B,IAAY,EAAQ;IAC7B,IAAI,CAACS,MAAM,CAAC,CAAC;IACb,IAAI,CAACoB,WAAW,CAAC7B,IAAI,EAAE,CAAC,EAAE,IAAI,CAACT,eAAe,CAAC;EACjD;EAKAM,KAAKA,CAACG,IAAY,EAAQ;IAExB,IAAIA,IAAI,OAAuB,EAAE;MAC/B,OAAO,IAAI,CAACd,YAAY,KAAK,CAAC,EAAE;QAC9B,MAAMc,IAAI,GAAG,IAAI,CAACf,MAAM,CAAC,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAACc,IAAI;QACpD,IAAIA,IAAI,OAAoB,IAAIA,IAAI,MAAkB,EAAE;UACtD;QACF;QAEA,IAAI,CAACd,YAAY,EAAE;MACrB;IACF;IAEA,MAAM4C,cAAc,GAAG,IAAI,CAACvC,eAAe;IAC3C,IAAI,CAACW,UAAU,CACbF,IAAI,EACJ,CAAC,EACD8B,cAAc,CAACzC,IAAI,EACnByC,cAAc,CAACxC,MAAM,EACrBwC,cAAc,CAACnC,QACjB,CAAC;EACH;EAKAoC,gBAAgBA,CAAC/B,IAAY,EAAEC,MAAc,EAAQ;IACnD,IAAI,CAACC,UAAU,CAACF,IAAI,EAAEC,MAAM,EAAER,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC;EAChE;EAEAgB,MAAMA,CAAA,EAAS;IACb,MAAMuB,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,MAAMW,KAAK,GAAG,IAAI,CAACZ,MAAM;IACzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,WAAW,EAAElC,CAAC,EAAE,EAAE;MACpC,MAAMO,IAAe,GAAGR,KAAK,CAACC,CAAC,CAAC;MAChC,IAAI,CAAC+B,WAAW,CAACxB,IAAI,CAACL,IAAI,EAAEK,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAAC;IAChD;IACA,IAAI,CAACnB,YAAY,GAAG,CAAC;EACvB;EAEA2C,WAAWA,CACT7B,IAAY,EACZC,MAAc,EACdgC,SAA4B,EACtB;IACN,IAAI,CAACjD,KAAK,GAAGgB,IAAI;IAEjB,IAAI,CAAClB,IAAI,IACPmB,MAAM,GAAG,CAAC,GACNiC,MAAM,CAACC,YAAY,CAACnC,IAAI,CAAC,CAACC,MAAM,CAACA,MAAM,CAAC,GACxCiC,MAAM,CAACC,YAAY,CAACnC,IAAI,CAAC;IAE/B,IAAIA,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAACoC,KAAK,CACRH,SAAS,CAAC5C,IAAI,EACd4C,SAAS,CAAC3C,MAAM,EAChB2C,SAAS,CAACzC,cAAc,EACxByC,SAAS,CAACvC,iBAAiB,EAC3BuC,SAAS,CAACtC,QACZ,CAAC;MACD,IAAI,CAACP,SAAS,CAACE,MAAM,IAAIW,MAAM;IACjC,CAAC,MAAM;MACL,IAAI,CAACb,SAAS,CAACC,IAAI,EAAE;MACrB,IAAI,CAACD,SAAS,CAACE,MAAM,GAAG,CAAC;IAC3B;IAEA,IAAI,IAAI,CAACH,cAAc,EAAE;MACvB8C,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;EACF;EAEAkC,OAAOA,CACLF,GAAW,EACXQ,SAA4B,EAC5BP,YAAqB,EACf;IACN,MAAMW,GAAG,GAAGZ,GAAG,CAACrB,MAAM;IACtB,MAAMkC,QAAQ,GAAG,IAAI,CAAClD,SAAS;IAE/B,IAAI,CAACJ,KAAK,GAAGyC,GAAG,CAACc,UAAU,CAACF,GAAG,GAAG,CAAC,CAAC;IAEpC,IAAI,EAAE,IAAI,CAACtD,YAAY,GAAG,IAAI,EAAE;MAC9B,CAAC,IAAI,CAACD,IAAI;MACV,IAAI,CAACD,IAAI,IAAI,IAAI,CAACC,IAAI;MACtB,IAAI,CAACA,IAAI,GAAG2C,GAAG;MACf,IAAI,CAAC1C,YAAY,GAAG,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACD,IAAI,IAAI2C,GAAG;IAClB;IAEA,IAAI,CAACC,YAAY,IAAI,CAAC,IAAI,CAAC9C,IAAI,EAAE;MAC/B0D,QAAQ,CAAChD,MAAM,IAAI+C,GAAG;MACtB;IACF;IAEA,MAAM;MAAE/C,MAAM;MAAEE,cAAc;MAAEE,iBAAiB;MAAEC;IAAS,CAAC,GAAGsC,SAAS;IACzE,IAAI5C,IAAI,GAAG4C,SAAS,CAAC5C,IAAI;IAEzB,IACE,CAACG,cAAc,IAAI,IAAI,IAAIE,iBAAiB,IAAI,IAAI,KACpD,IAAI,CAACP,cAAc,EACnB;MACA8C,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;IAMA,IAAIK,CAAC,GAAG2B,GAAG,CAACe,OAAO,CAAC,IAAI,CAAC;IACzB,IAAIC,IAAI,GAAG,CAAC;IAIZ,IAAI3C,CAAC,KAAK,CAAC,EAAE;MACX,IAAI,CAACsC,KAAK,CAAC/C,IAAI,EAAEC,MAAM,EAAEE,cAAc,EAAEE,iBAAiB,EAAEC,QAAQ,CAAC;IACvE;IAGA,OAAOG,CAAC,KAAK,CAAC,CAAC,EAAE;MACfwC,QAAQ,CAACjD,IAAI,EAAE;MACfiD,QAAQ,CAAChD,MAAM,GAAG,CAAC;MACnBmD,IAAI,GAAG3C,CAAC,GAAG,CAAC;MAKZ,IAAI2C,IAAI,GAAGJ,GAAG,IAAIhD,IAAI,KAAKI,SAAS,EAAE;QACpC,IAAI,CAAC2C,KAAK,CAAC,EAAE/C,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAEM,QAAQ,CAAC;MAC7C;MACAG,CAAC,GAAG2B,GAAG,CAACe,OAAO,CAAC,IAAI,EAAEC,IAAI,CAAC;IAC7B;IACAH,QAAQ,CAAChD,MAAM,IAAI+C,GAAG,GAAGI,IAAI;EAC/B;EAEAL,KAAKA,CACH/C,IAAwB,EACxBC,MAA0B,EAC1BE,cAAkC,EAClCE,iBAAkC,EAClCC,QAA4B,EACtB;IAAA,IAAA+C,UAAA;IACN,CAAAA,UAAA,OAAI,CAAC9D,IAAI,qBAAT8D,UAAA,CAAWC,IAAI,CACb,IAAI,CAACvD,SAAS,EACdC,IAAI,EACJC,MAAM,EACNE,cAAc,EACdE,iBAAiB,EACjBC,QACF,CAAC;EACH;EAEAiD,qBAAqBA,CAAA,EAAS;IAC5B,MAAMZ,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,IACE8C,WAAW,KAAK,CAAC,IACjB,IAAI,CAAC/C,MAAM,CAAC+C,WAAW,GAAG,CAAC,CAAC,CAAChC,IAAI,OAAuB,EACxD;MACA,IAAI,CAACd,YAAY,EAAE;IACrB;EACF;EAEA2D,mBAAmBA,CAAA,EAAS;IAC1B,MAAMb,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,IACE8C,WAAW,KAAK,CAAC,IACjB,IAAI,CAAC/C,MAAM,CAAC+C,WAAW,GAAG,CAAC,CAAC,CAAChC,IAAI,OAAwB,EACzD;MACA,IAAI,CAACd,YAAY,EAAE;IACrB;EACF;EAEA4D,WAAWA,CAAA,EAAW;IACpB,MAAMd,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,OAAO8C,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC/C,MAAM,CAAC+C,WAAW,GAAG,CAAC,CAAC,CAAChC,IAAI,GAAG,IAAI,CAAChB,KAAK;EAC3E;EAMA+D,eAAeA,CAAA,EAAW;IACxB,MAAMf,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,IAAI8D,KAAK,GAAG,CAAC;IACb,IAAIhB,WAAW,KAAK,CAAC,EAAE,OAAO,IAAI,CAAChD,KAAK,OAAuB,GAAG,CAAC,GAAG,CAAC;IACvE,KAAK,IAAIc,CAAC,GAAGkC,WAAW,GAAG,CAAC,EAAElC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAI,IAAI,CAACb,MAAM,CAACa,CAAC,CAAC,CAACE,IAAI,OAAuB,EAAE;QAC9C;MACF;MACAgD,KAAK,EAAE;IACT;IACA,OAAOA,KAAK,KAAKhB,WAAW,IAAI,IAAI,CAAChD,KAAK,OAAuB,GAC7DgE,KAAK,GAAG,CAAC,GACTA,KAAK;EACX;EAQAC,sBAAsBA,CAAA,EAAW;IAC/B,MAAMpD,KAAK,GAAG,IAAI,CAACZ,MAAM;IACzB,MAAM+C,WAAW,GAAG,IAAI,CAAC9C,YAAY;IACrC,IAAI8C,WAAW,KAAK,CAAC,EAAE;MAErB,MAAMkB,MAAM,GAAGrD,KAAK,CAACmC,WAAW,GAAG,CAAC,CAAC,CAAChC,IAAI;MAC1C,IAAIkD,MAAM,OAAuB,EAAE;MACnC,IAAIlB,WAAW,GAAG,CAAC,EAAE;QACnB,OAAOnC,KAAK,CAACmC,WAAW,GAAG,CAAC,CAAC,CAAChC,IAAI;MACpC,CAAC,MAAM;QACL,OAAO,IAAI,CAAChB,KAAK;MACnB;IACF;EAGF;EAEAmE,UAAUA,CAAA,EAAY;IACpB,OAAO,IAAI,CAACjE,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAACF,KAAK;EAChD;EAyBAoE,WAAWA,CAACC,GAAoB,EAAEC,EAAc,EAAE;IAChD,IAAI,CAAC,IAAI,CAAC1E,IAAI,EAAE;MACd0E,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,MAAM,CAAC,OAAO,EAAEF,GAAG,CAAC;IAEzB,MAAM7D,cAAc,GAAG6D,GAAG,CAAC7D,cAAc;IACzC,MAAMyC,SAAS,GAAG,IAAI,CAAC1C,eAAe;IACtC,IAAIC,cAAc,EAAE;MAClB,IAAI,CAACL,cAAc,GAAG,KAAK;MAC3B8C,SAAS,CAACzC,cAAc,GAAGA,cAAc;IAC3C;IACA8D,EAAE,CAAC,CAAC;IAEJ,IAAI9D,cAAc,EAAE;MAClB,IAAI,CAACL,cAAc,GAAG,IAAI;MAC1B8C,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;IACA,IAAI,CAAC8D,MAAM,CAAC,KAAK,EAAEF,GAAG,CAAC;EACzB;EAOAE,MAAMA,CAACC,IAAqB,EAAEH,GAAoB,EAAQ;IACxD,IAAI,CAAC,IAAI,CAACzE,IAAI,EAAE;IAIhB,IAAI,CAAC6E,kBAAkB,CAACD,IAAI,EAAEH,GAAG,EAAE,CAAC,CAAC;EACvC;EAEAK,gBAAgBA,CACdF,IAAqB,EACrBH,GAAoB,EACpBM,YAAoB,EACd;IACN,IAAI,CAAC,IAAI,CAAC/E,IAAI,EAAE;IAEhB,IAAI,CAAC6E,kBAAkB,CAACD,IAAI,EAAEH,GAAG,EAAEM,YAAY,CAAC;EAClD;EAMAC,UAAUA,CAACJ,IAAqB,EAAEH,GAAQ,EAAEC,EAAc,EAAQ;IAChE,IAAI,IAAI,CAAC1E,IAAI,EAAE;MACb,IAAI,CAAC2E,MAAM,CAACC,IAAI,EAAEH,GAAG,CAAC;IACxB;IAEAC,EAAE,CAAC,CAAC;EACN;EAEAG,kBAAkBA,CAACD,IAAqB,EAAEH,GAAQ,EAAEM,YAAoB,EAAE;IACxE,MAAME,GAAG,GAAGR,GAAG,CAACG,IAAI,CAAC;IACrB,MAAMM,MAAM,GAAG,IAAI,CAACvE,eAAe;IAEnC,IAAIsE,GAAG,EAAE;MACPC,MAAM,CAACzE,IAAI,GAAGwE,GAAG,CAACxE,IAAI;MAEtByE,MAAM,CAACxE,MAAM,GAAGyE,IAAI,CAACC,GAAG,CAACH,GAAG,CAACvE,MAAM,GAAGqE,YAAY,EAAE,CAAC,CAAC;MACtDG,MAAM,CAACnE,QAAQ,GAAG0D,GAAG,CAAC1D,QAAQ;IAChC;EACF;EAEAsE,gBAAgBA,CAAA,EAAW;IACzB,MAAMpE,KAAK,GAAG,IAAI,CAACZ,MAAM;IACzB,MAAM+C,WAAW,GAAG,IAAI,CAAC9C,YAAY;IAErC,IAAIgF,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI7B,GAAG,GAAG,CAAC;IACX,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,WAAW,EAAElC,CAAC,EAAE,EAAE;MACpC,MAAMO,IAAI,GAAGR,KAAK,CAACC,CAAC,CAAC;MACrB,IAAIO,IAAI,CAACL,IAAI,OAAuB,EAAE;QACpCkE,SAAS,GAAG7B,GAAG;MACjB;MACAA,GAAG,IAAIhC,IAAI,CAACJ,MAAM;IACpB;IAEA,OAAOiE,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC9E,SAAS,CAACE,MAAM,GAAG+C,GAAG,GAAGA,GAAG,GAAG,CAAC,GAAG6B,SAAS;EAC7E;EAEAC,cAAcA,CAAA,EAAW;IACvB,IAAInB,KAAK,GAAG,CAAC;IAEb,MAAMnD,KAAK,GAAG,IAAI,CAACZ,MAAM;IACzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACZ,YAAY,EAAEY,CAAC,EAAE,EAAE;MAC1C,IAAID,KAAK,CAACC,CAAC,CAAC,CAACE,IAAI,OAAuB,EAAE;QACxCgD,KAAK,EAAE;MACT;IACF;IAEA,OAAO,IAAI,CAAC5D,SAAS,CAACC,IAAI,GAAG2D,KAAK;EACpC;AACF;AAACoB,OAAA,CAAAC,OAAA,GAAA5F,MAAA"}