{"version": 3, "names": ["_context", "require", "_t", "VISITOR_KEYS", "traverseNode", "node", "opts", "scope", "state", "path", "<PERSON><PERSON><PERSON><PERSON>", "visitSelf", "keys", "type", "context", "TraversalContext", "parent<PERSON><PERSON>", "visitQueue", "key", "visit"], "sources": ["../src/traverse-node.ts"], "sourcesContent": ["import TraversalContext from \"./context.ts\";\nimport type { ExplodedTraverseOptions } from \"./index.ts\";\nimport type NodePath from \"./path/index.ts\";\nimport type Scope from \"./scope/index.ts\";\nimport type * as t from \"@babel/types\";\nimport { VISITOR_KEYS } from \"@babel/types\";\n\n/**\n * Traverse the children of given node\n * @param {Node} node\n * @param {TraverseOptions} opts The traverse options used to create a new traversal context\n * @param {scope} scope A traversal scope used to create a new traversal context. When opts.noScope is true, scope should not be provided\n * @param {any} state A user data storage provided as the second callback argument for traversal visitors\n * @param {NodePath} path A NodePath of given node\n * @param {Record<string, boolean>} skipKeys A map from key names to whether that should be skipped during traversal. The skipKeys are applied to every descendants\n * @returns {boolean} Whether the traversal stops early\n\n * @note This function does not visit the given `node`.\n */\nexport function traverseNode<S = unknown>(\n  node: t.Node,\n  opts: ExplodedTraverseOptions<S>,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n  visitSelf?: boolean,\n): boolean {\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return false;\n\n  const context = new TraversalContext(scope, opts, state, path);\n  if (visitSelf) {\n    if (skipKeys?.[path.parentKey]) return false;\n    return context.visitQueue([path]);\n  }\n\n  for (const key of keys) {\n    if (skipKeys?.[key]) continue;\n    if (context.visit(node, key)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,EAAA,GAAAD,OAAA;AAA4C;EAAnCE;AAAY,IAAAD,EAAA;AAcd,SAASE,YAAYA,CAC1BC,IAAY,EACZC,IAAgC,EAChCC,KAAa,EACbC,KAAW,EACXC,IAAe,EACfC,QAAkC,EAClCC,SAAmB,EACV;EACT,MAAMC,IAAI,GAAGT,YAAY,CAACE,IAAI,CAACQ,IAAI,CAAC;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,OAAO,GAAG,IAAIC,gBAAgB,CAACR,KAAK,EAAED,IAAI,EAAEE,KAAK,EAAEC,IAAI,CAAC;EAC9D,IAAIE,SAAS,EAAE;IACb,IAAID,QAAQ,YAARA,QAAQ,CAAGD,IAAI,CAACO,SAAS,CAAC,EAAE,OAAO,KAAK;IAC5C,OAAOF,OAAO,CAACG,UAAU,CAAC,CAACR,IAAI,CAAC,CAAC;EACnC;EAEA,KAAK,MAAMS,GAAG,IAAIN,IAAI,EAAE;IACtB,IAAIF,QAAQ,YAARA,QAAQ,CAAGQ,GAAG,CAAC,EAAE;IACrB,IAAIJ,OAAO,CAACK,KAAK,CAACd,IAAI,EAAEa,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd"}