{
  // 统一使用 \n 换行符
  "files.eol": "\n",
  // 缩进宽度为 2
  "editor.tabSize": 2,
  // 保存时自动格式化
  "editor.formatOnSave": true,
  // 默认使用 Prettier 格式化
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // ESLint 配置
  "eslint.validate": ["javascript", "typescript"],
  "editor.codeActionsOnSave": {
    // 保存时自动修复 ESLint 错误
    "source.fixAll.eslint": true
  },
  // 使用项目的 ts 版本
  "typescript.tsdk": "node_modules/typescript/lib"
}
