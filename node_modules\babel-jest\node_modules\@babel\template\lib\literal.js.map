{"version": 3, "names": ["_options", "require", "_parse", "_populate", "literalTemplate", "formatter", "tpl", "opts", "metadata", "names", "buildLiteralData", "arg", "defaultReplacements", "for<PERSON>ach", "replacement", "i", "replacements", "normalizeReplacements", "Object", "keys", "key", "prototype", "hasOwnProperty", "call", "Error", "unwrap", "populatePlaceholders", "assign", "prefix", "raw", "join", "includes", "code", "buildTemplateCode", "parseAndBuildMetadata", "parser", "placeholder<PERSON><PERSON><PERSON><PERSON>", "Set", "concat", "Array", "from", "placeholder<PERSON><PERSON><PERSON>", "preserveComments", "syntacticPlaceholders", "length", "value", "push"], "sources": ["../src/literal.ts"], "sourcesContent": ["import type { Formatter } from \"./formatters.ts\";\nimport type { TemplateReplacements, TemplateOpts } from \"./options.ts\";\nimport { normalizeReplacements } from \"./options.ts\";\nimport parseAndBuildMetadata from \"./parse.ts\";\nimport populatePlaceholders from \"./populate.ts\";\n\nexport default function literalTemplate<T>(\n  formatter: Formatter<T>,\n  tpl: Array<string>,\n  opts: TemplateOpts,\n): (_: Array<unknown>) => (_: unknown) => T {\n  const { metadata, names } = buildLiteralData(formatter, tpl, opts);\n\n  return arg => {\n    const defaultReplacements: TemplateReplacements = {};\n    arg.forEach((replacement, i) => {\n      defaultReplacements[names[i]] = replacement;\n    });\n\n    return (arg: unknown) => {\n      const replacements = normalizeReplacements(arg);\n\n      if (replacements) {\n        Object.keys(replacements).forEach(key => {\n          if (Object.prototype.hasOwnProperty.call(defaultReplacements, key)) {\n            throw new Error(\"Unexpected replacement overlap.\");\n          }\n        });\n      }\n\n      return formatter.unwrap(\n        populatePlaceholders(\n          metadata,\n          replacements\n            ? Object.assign(replacements, defaultReplacements)\n            : defaultReplacements,\n        ),\n      );\n    };\n  };\n}\n\nfunction buildLiteralData<T>(\n  formatter: Formatter<T>,\n  tpl: Array<string>,\n  opts: TemplateOpts,\n) {\n  let prefix = \"BABEL_TPL$\";\n\n  const raw = tpl.join(\"\");\n\n  do {\n    // If there are cases where the template already contains $$BABEL_TPL$0 or any other\n    // matching pattern, we keep adding \"$$\" characters until a unique prefix\n    // is found.\n    prefix = \"$$\" + prefix;\n  } while (raw.includes(prefix));\n\n  const { names, code } = buildTemplateCode(tpl, prefix);\n\n  const metadata = parseAndBuildMetadata(formatter, formatter.code(code), {\n    parser: opts.parser,\n\n    // Explicitly include our generated names in the whitelist so users never\n    // have to think about whether their placeholder pattern will match.\n    placeholderWhitelist: new Set(\n      names.concat(\n        opts.placeholderWhitelist ? Array.from(opts.placeholderWhitelist) : [],\n      ),\n    ),\n    placeholderPattern: opts.placeholderPattern,\n    preserveComments: opts.preserveComments,\n    syntacticPlaceholders: opts.syntacticPlaceholders,\n  });\n\n  return { metadata, names };\n}\n\nfunction buildTemplateCode(\n  tpl: Array<string>,\n  prefix: string,\n): { names: Array<string>; code: string } {\n  const names = [];\n\n  let code = tpl[0];\n\n  for (let i = 1; i < tpl.length; i++) {\n    const value = `${prefix}${i - 1}`;\n    names.push(value);\n\n    code += value + tpl[i];\n  }\n\n  return { names, code };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAEe,SAASG,eAAeA,CACrCC,SAAuB,EACvBC,GAAkB,EAClBC,IAAkB,EACwB;EAC1C,MAAM;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGC,gBAAgB,CAACL,SAAS,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAElE,OAAOI,GAAG,IAAI;IACZ,MAAMC,mBAAyC,GAAG,CAAC,CAAC;IACpDD,GAAG,CAACE,OAAO,CAAC,CAACC,WAAW,EAAEC,CAAC,KAAK;MAC9BH,mBAAmB,CAACH,KAAK,CAACM,CAAC,CAAC,CAAC,GAAGD,WAAW;IAC7C,CAAC,CAAC;IAEF,OAAQH,GAAY,IAAK;MACvB,MAAMK,YAAY,GAAG,IAAAC,8BAAqB,EAACN,GAAG,CAAC;MAE/C,IAAIK,YAAY,EAAE;QAChBE,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAACH,OAAO,CAACO,GAAG,IAAI;UACvC,IAAIF,MAAM,CAACG,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,mBAAmB,EAAEQ,GAAG,CAAC,EAAE;YAClE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;UACpD;QACF,CAAC,CAAC;MACJ;MAEA,OAAOnB,SAAS,CAACoB,MAAM,CACrB,IAAAC,iBAAoB,EAClBlB,QAAQ,EACRQ,YAAY,GACRE,MAAM,CAACS,MAAM,CAACX,YAAY,EAAEJ,mBAAmB,CAAC,GAChDA,mBACN,CACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;AAEA,SAASF,gBAAgBA,CACvBL,SAAuB,EACvBC,GAAkB,EAClBC,IAAkB,EAClB;EACA,IAAIqB,MAAM,GAAG,YAAY;EAEzB,MAAMC,GAAG,GAAGvB,GAAG,CAACwB,IAAI,CAAC,EAAE,CAAC;EAExB,GAAG;IAIDF,MAAM,GAAG,IAAI,GAAGA,MAAM;EACxB,CAAC,QAAQC,GAAG,CAACE,QAAQ,CAACH,MAAM,CAAC;EAE7B,MAAM;IAAEnB,KAAK;IAAEuB;EAAK,CAAC,GAAGC,iBAAiB,CAAC3B,GAAG,EAAEsB,MAAM,CAAC;EAEtD,MAAMpB,QAAQ,GAAG,IAAA0B,cAAqB,EAAC7B,SAAS,EAAEA,SAAS,CAAC2B,IAAI,CAACA,IAAI,CAAC,EAAE;IACtEG,MAAM,EAAE5B,IAAI,CAAC4B,MAAM;IAInBC,oBAAoB,EAAE,IAAIC,GAAG,CAC3B5B,KAAK,CAAC6B,MAAM,CACV/B,IAAI,CAAC6B,oBAAoB,GAAGG,KAAK,CAACC,IAAI,CAACjC,IAAI,CAAC6B,oBAAoB,CAAC,GAAG,EACtE,CACF,CAAC;IACDK,kBAAkB,EAAElC,IAAI,CAACkC,kBAAkB;IAC3CC,gBAAgB,EAAEnC,IAAI,CAACmC,gBAAgB;IACvCC,qBAAqB,EAAEpC,IAAI,CAACoC;EAC9B,CAAC,CAAC;EAEF,OAAO;IAAEnC,QAAQ;IAAEC;EAAM,CAAC;AAC5B;AAEA,SAASwB,iBAAiBA,CACxB3B,GAAkB,EAClBsB,MAAc,EAC0B;EACxC,MAAMnB,KAAK,GAAG,EAAE;EAEhB,IAAIuB,IAAI,GAAG1B,GAAG,CAAC,CAAC,CAAC;EAEjB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,GAAG,CAACsC,MAAM,EAAE7B,CAAC,EAAE,EAAE;IACnC,MAAM8B,KAAK,GAAI,GAAEjB,MAAO,GAAEb,CAAC,GAAG,CAAE,EAAC;IACjCN,KAAK,CAACqC,IAAI,CAACD,KAAK,CAAC;IAEjBb,IAAI,IAAIa,KAAK,GAAGvC,GAAG,CAACS,CAAC,CAAC;EACxB;EAEA,OAAO;IAAEN,KAAK;IAAEuB;EAAK,CAAC;AACxB"}