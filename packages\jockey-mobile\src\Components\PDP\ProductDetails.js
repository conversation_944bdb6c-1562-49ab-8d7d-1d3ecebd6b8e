import {
  FlatList,
  Dimensions,
  Image,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Easing,
} from 'react-native';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  useProductDetail,
  useProductVariations,
  currencyHelper,
} from '@appmaker-xyz/shopify';
import { ScrollView } from 'react-native';
import { useState } from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Svg, {
  Circle,
  Path,
  Defs,
  LinearGradient as LinearGradientSvg,
  Stop,
} from 'react-native-svg';
import Edd from './Edd';
import RenderRichText from '../CommonComponents/RenderRichText';
import HTML from 'react-native-render-html';
import { useDataSourceV2, usePageMetaDataState, runDataSource } from '@appmaker-xyz/core';
import ProductDescription from './ProductDescription';
import { SvgUri } from 'react-native-svg';
import { Pressable } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import {
  fonts,
  getHorizontalPadding,
  getVerticalPadding,
  heightPixel,
  widthPixel,
} from '../../styles';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ShopifyImage from '../ShopifyImage';
import MaskedView from '@react-native-masked-view/masked-view';
import { ImageBackground } from 'react-native';
import { useProducts } from '@appmaker-xyz/shopify'
import { Animated } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import PrimeSelectionProductCard from '../common/PrimeSelectionProductCard';
import ProductModal from '../common/ProductModal';
const windowWidth = Dimensions.get('window').width;

const dataSource = {
  attributes: {},
  source: 'shopify',
};

const ITEM_WIDTH = windowWidth / 3 - 10; // Calculate item width dynamically
const ITEM_HEIGHT = ITEM_WIDTH * 1.4; // Adjust aspect ratio if needed

const calculateFontSize = () => {
  const minFontSize = 14;
  const maxFontSize = 18;
  const fontSize = 14 * (windowWidth / 1440);
  return Math.max(minFontSize, Math.min(fontSize, maxFontSize));
};

const calculateLineHeight = () => {
  const minLineHeight = 15;
  const maxLineHeight = 20;
  const lineHeight = (1.25 * windowWidth) / 100;
  return Math.max(minLineHeight, Math.min(lineHeight, maxLineHeight));
};

export default function ProductDetails(props) {
  const insets = useSafeAreaInsets();

  const { data } = usePageMetaDataState("product-meta");
  const [modalVisible, setModalVisible] = useState(false);
  const [countryImage, setCountryImage] = useState();
  const [productIdealForImage, setProductIdealForImage] = useState([]);
  const [productWashingInstructionImage, setProductWashingInstructionImage] = useState([]);
  const [productFitAndFeelImage, setProductFitAndFeelImage] = useState([]);
  const [isVariantModalVisible, setVariantModalVisible] = useState(false);
  const [productData, setProductData] = useState('');
  const [selectedProductData, setSelectedProductData] = useState({});
  const [allVariants, setAllVariants] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [selectedLook, setSelectedLook] = useState([]);

  const blinkAnim = useRef(new Animated.Value(0)).current;
  const bottomSheetRef = useRef(null);

  const rawValue = data?.product?.shopthelook_hotspot_products_links?.value;

  try {
    const parsed = JSON.parse(rawValue);
    console.log("First GIDnew:", parsed[0]);
  } catch (error) {
    console.error("Failed to parse rawValue:", error);
  }

  const firstClick = () => {
    try {
      const parsed = JSON.parse(rawValue);
      let setValue = [parsed[0], parsed[1]];
      setSelectedLook(setValue);
    } catch (error) {
      console.error("Error in firstClick - Failed to parse rawValue:", error);
    }
  };

  const secondClick = () => {
    try {
      const parsed = JSON.parse(rawValue);
      let setValue = [parsed[1], parsed[0]];
      setSelectedLook(setValue);
    } catch (error) {
      console.error("Error in secondClick - Failed to parse rawValue:", error);
    }
  };

  const productIds = selectedLook

  const { productList, error, isFetching } = useProducts({ query: { productIds } });
console.log("product", productList)
  // const rawValue = data?.product?.shopthelook_hotspot_products_links?.value;

  // const parsedProductIds = useMemo(() => {
  //   if (!rawValue) return [];

  //   try {
  //     const parsed = JSON.parse(rawValue);
  //     console.log("First GID:", parsed[0]);
  //     return parsed;
  //   } catch (error) {
  //     console.error("Failed to parse rawValue:", error);
  //     return [];
  //   }
  // }, [rawValue]);



  // const productIds = selectedLook || parsedProductIds;

  // const { productList, error } = useProducts({
  //   query: { productIds },
  // });

  const hasFetchedVariantsRef = useRef(false);

  const LoadingView = () => (
    <View style={styles.loadingContainerStyle}>
      <Text>Loading.....</Text>
      {/* <ActivityIndicator color="#212121" size="large" /> */}
      {/* <Layout loading={true} loadingLayout={loadingLayout} /> */}
    </View>
  );

  useEffect(() => {
    if (!hasFetchedVariantsRef.current && productList?.length > 0) {
      handleAllVariantFetch();
      hasFetchedVariantsRef.current = true;
    }
  }, [productList]);

  const handleAllVariantFetch = async () => {
    if (!productList || productList.length === 0) return;

    try {
      const variantArrays = await Promise.all(
        productList.map(async (element) => {
          const idsArray = JSON.parse(element?.node?.color_variant?.value || '[]');
          const [variantsResponse] = await runDataSource(
            {
              dataSource,
            },
            {
              methodName: 'gqlQuery',
              params: {
                query: `
              {
                nodes(ids: ${JSON.stringify(idsArray)}) {
                  ... on Product {
                    handle
                    title
                    totalInventory
                    tags
                    id
                    images(first: 10) {
                      edges {
                        node {
                          url
                        }
                      }
                    }
                    metafields(identifiers: [
                      { key: "style_number", namespace: "custom" },
                      { key: "product_color_name", namespace: "custom" }
                    ]) {
                      value
                    }
                    style_number: metafield(key: "style_number", namespace: "custom") {
                      value
                    }
                    color_variant: metafield(key: "color_variant", namespace: "custom") {
                      value
                      reference {
                        ... on Product {
                          images(first: 7) {
                            nodes {
                              url
                              altText
                            }
                          }
                          handle
                          totalInventory
                          title
                          tags
                          options(first: 10) {
                            values
                            name
                          }
                          variants(first: 20) {
                            edges {
                              node {
                                id
                                sku
                                title
                                availableForSale
                                quantityAvailable
                                selectedOptions {
                                  name
                                  value
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                    priceRange {
                      minVariantPrice {
                        amount
                        currencyCode
                      }
                    }
                    variants(first: 20) {
                      edges {
                        node {
                          id
                          sku
                          title
                          availableForSale
                          quantityAvailable
                          selectedOptions {
                            name
                            value
                          }
                        }
                      }
                    }
                    options(first: 10) {
                      values
                      name
                    }
                  }
                }
              }`,
              },
            }
          );

          const nodes = variantsResponse?.data?.data?.nodes || [];

          const nodeIndex = nodes.findIndex((node) => node?.id === element?.id);

          if (nodeIndex !== -1) {
            const [nodeToMove] = nodes.splice(nodeIndex, 1);
            nodes.unshift(nodeToMove);
          }

          return nodes;
        })
      );

      setAllVariants(variantArrays);
      console.log("All Variants Fetched:", variantArrays);
    } catch (err) {
      console.error("Error fetching variant data:", err);
    }
  };

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const moveProductToFirst = useCallback((response, productIdToMoveFirst) => {
    let productToMove;
    const filteredNodes = [];

    response?.nodes?.forEach((node) => {
      if (node?.id === productIdToMoveFirst) {
        productToMove = node;
      } else {
        filteredNodes.push(node);
      }
    });

    if (productToMove) {
      return {
        ...response,
        nodes: [productToMove, ...filteredNodes],
      };
    }
    return response;
  }, []);

  useEffect(() => {
    const loopAnimation = () => {
      blinkAnim.setValue(0);
      Animated.timing(blinkAnim, {
        toValue: 1,
        duration: 950,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      }).start(() => {
        loopAnimation();
      });
    };

    loopAnimation();
  }, [blinkAnim]);

  const backgroundOpacity = blinkAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0],
  });

  const backgroundScale = blinkAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1.55],
  });

  useEffect(() => {

    if (data?.product?.style_note_image?.value) {
      fetchData([data?.product?.style_note_image?.value.trim()])
        .then((result) => setCountryImage(result.data.nodes[0].image.src))
        .catch((error) => console.log(error));
    }

    if (data?.product?.product_is_ideal_for_image?.value) {
      const idealData = JSON.parse(
        data?.product?.product_is_ideal_for_image?.value,
      ).map((value) => {
        return value.trim();
      });

      fetchData(idealData)
        .then((result) => {
          setProductIdealForImage(result.data.nodes)
        })
        .catch((error) => console.log(error));
    }

    if (data?.product?.product_fit_and_feel_image?.value) {
      const fitData = JSON.parse(
        data?.product?.product_fit_and_feel_image?.value,
      ).map((value) => {
        return value.trim();
      });


      fetchData(fitData)
        .then((result) => setProductFitAndFeelImage(result.data.nodes))
        .catch((error) => console.log(error));
    }

    if (data?.product?.washing_instruction_image?.value) {
      const washingData = JSON.parse(
        data?.product?.washing_instruction_image?.value,
      ).map((value) => {
        return value.trim();
      });

      fetchData(washingData)
        .then((result) => setProductWashingInstructionImage(result.data.nodes))
        .catch((error) => console.log(error));
    }

    if (product?.node?.pack_of_product_to_add?.value) {
      fetchProductDataFromId(product?.node?.pack_of_product_to_add?.value)
        .then((result) => setProductData(result.data.product))
        .catch((error) => console.log(error));
    }


  }, [data])

  const { product } = useProductDetail(props);
  const { selectedVariant } = useProductVariations(props);
  const returnPolicyData = data?.product?.return_policy?.value ? JSON.parse(data?.product?.return_policy?.value) : null;
  const isReturnable = data?.product?.returnable_products?.value === 'true';

  console.log("policyData", isReturnable)
  const parsedData =
    data?.product?.product_fit_and_feel &&
    JSON.parse(data?.product?.product_fit_and_feel?.value);

  const extractText = (htmlString) => {
    const h3Text = htmlString.match(/<h3>(.*?)<\/h3>/)?.[1] || '';
    const pText = htmlString.match(/<p>(.*?)<\/p>/)?.[1] || '';
    return { h3Text, pText };
  };

  const openVariantModal = (selectedProductData, index, value) => {
    bottomSheetRef.current.close();
    const foundIndex = allVariants.findIndex(
      (item) => item?.[0]?.id === value.id,
    );

    setActiveIndex(foundIndex != -1 ? foundIndex : index);
    setSelectedProductData(selectedProductData);
    setVariantModalVisible(true);
  };
  // const onOpen = () => {
  //   if (handleOpen) handleOpen();
  // };

  // const onClose = () => {
  //   if (handleCloseModal) handleCloseModal();
  // };
  const renderItem = ({ item, index }) => {

    return (
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          width: windowWidth / 3 - widthPixel(14),
          paddingVertical: heightPixel(10),
          paddingHorizontal: heightPixel(8),
          borderWidth: 1,
          borderColor: 'rgba(34,31,32,.11)',
          borderRadius: widthPixel(10),
        }}>
        {item.image.src.includes('.svg') ? (
          <SvgUri
            width={widthPixel(36)}
            height={widthPixel(36)}
            uri={item.image.src}
          />
        ) : (
          <Image
            source={{ uri: item.image.src }}
            style={{
              width: widthPixel(36),
              height: widthPixel(36),
              resizeMode: 'cover',
            }}
          />
        )}
        {parsedData && parsedData.length > 0 && (
          <>
            <Text
              style={{
                fontSize: fonts._14,
                color: '#221f20',
                fontFamily: fonts.FONT_FAMILY.Medium,
                textAlign: 'center',
              }}>
              {extractText(parsedData[index]).h3Text}
            </Text>

            <Text
              style={{
                fontSize: fonts._11,
                color: '#221f2099',
                fontFamily: fonts.FONT_FAMILY.Regular,
                textAlign: 'center',
              }}>
              {extractText(parsedData[index]).pText}
            </Text>
          </>
        )}
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator
        keyboardShouldPersistTaps="handled">
        <View style={styles.paddingContainer}>
          {/* product fit and feel */}

          {productFitAndFeelImage.length > 0 &&
            data?.product?.product_fit_and_feel?.value?.length > 0 && (
              <View>
                <Text
                  style={{
                    paddingVertical: heightPixel(5),
                    fontSize: fonts._16,
                    color: '#221f20',
                    fontFamily: fonts.FONT_FAMILY.SemiBold,
                    letterSpacing: 0,
                  }}>
                  Product Fit and Feel{' '}
                </Text>
                {getVerticalPadding(8)}

                <View style={styles.gridContainer}>
                  <FlatList
                    horizontal
                    ItemSeparatorComponent={getHorizontalPadding(5)}
                    data={productFitAndFeelImage}
                    showsHorizontalScrollIndicator={false}
                    renderItem={renderItem}
                  />
                </View>
              </View>
            )}

          {/* pack of product */}

          {product?.node?.pack_of_product_number?.value &&
            productData?.compareAtPriceRange?.minVariantPrice?.amount &&
            (
              <View style={styles.packOfProductContainer}>
                <Text
                  style={{
                    fontSize: fonts._14,
                    fontFamily: fonts.FONT_FAMILY.Medium,
                  }}>
                  Save more with
                </Text>

                {getVerticalPadding(8)}

                <View
                  style={{
                    flexDirection: 'row',
                    padding: widthPixel(8),
                    flex: 1,
                    borderRadius: widthPixel(10),
                    borderWidth: 1,
                    borderColor: 'rgba(34, 31, 32, .17)',
                    alignItems: 'center',
                  }}>
                  <Svg
                    xmlns="http://www.w3.org/2000/svg"
                    width={24}
                    height={29}
                    viewBox="0 0 24 29"
                    fill="none">
                    <Path
                      d="M20.6806 11.7916H10.6231C11.0882 11.257 11.3708 10.5597 11.3708 9.79713C11.3708 8.11907 10.0056 6.75391 8.32754 6.75391C6.64948 6.75391 5.28431 8.11907 5.28431 9.79713C5.28431 10.5597 5.56689 11.257 6.03191 11.7916H3.63109C2.18031 11.7916 1 12.9719 1 14.4227V17.5273C1 17.818 1.23559 18.0535 1.52622 18.0535C1.81685 18.0535 2.05244 17.818 2.05244 17.5273V14.4227C2.05244 13.5522 2.76062 12.844 3.63109 12.844H18.3652C19.2357 12.844 19.9439 13.5522 19.9439 14.4227V17.2643H14.4712C13.0204 17.2643 11.8401 18.4446 11.8401 19.8954C11.8401 21.3461 13.0204 22.5265 14.4712 22.5265H19.9439V25.368C19.9439 26.2385 19.2357 26.9467 18.3652 26.9467H3.63109C2.76062 26.9467 2.05244 26.2385 2.05244 25.368V22.2592C2.05244 21.9686 1.81685 21.733 1.52622 21.733C1.23559 21.733 1 21.9686 1 22.2592V25.368C1 26.8188 2.18031 27.9991 3.63109 27.9991H20.6806C22.1313 27.9991 23.3116 26.8188 23.3116 25.368V14.4227C23.3116 12.9719 22.1313 11.7916 20.6806 11.7916ZM8.32754 7.8064C9.42528 7.8064 10.3183 8.69944 10.3183 9.79718C10.3183 10.8949 9.42528 11.788 8.32754 11.788C7.22984 11.788 6.33675 10.8949 6.33675 9.79718C6.33675 8.69944 7.22979 7.8064 8.32754 7.8064ZM20.4687 12.844H20.6806C21.551 12.844 22.2592 13.5522 22.2592 14.4227V17.2643H20.9963V14.4227C20.9963 13.8309 20.7997 13.2841 20.4687 12.844ZM12.8925 19.8954C12.8925 19.0249 13.6007 18.3167 14.4712 18.3167H22.2592V21.474H14.4712C13.6007 21.474 12.8925 20.7658 12.8925 19.8954ZM20.6806 26.9467H20.4687C20.7997 26.5066 20.9963 25.9599 20.9963 25.368V22.5265H22.2592V25.368C22.2592 26.2385 21.551 26.9467 20.6806 26.9467Z"
                      fill="#221F20"
                      fillOpacity="0.85"
                    />
                    <Path
                      d="M20.6806 11.7416H10.7304C11.1615 11.21 11.4208 10.5335 11.4208 9.79713C11.4208 8.09146 10.0332 6.70391 8.32754 6.70391C6.62186 6.70391 5.23431 8.09146 5.23431 9.79713C5.23431 10.5335 5.49356 11.21 5.92462 11.7416H3.63109C2.15269 11.7416 0.95 12.9443 0.95 14.4227V17.5273C0.95 17.8456 1.20797 18.1035 1.52622 18.1035C1.84446 18.1035 2.10244 17.8456 2.10244 17.5273V14.4227C2.10244 13.5798 2.78823 12.894 3.63109 12.894H18.3652C19.2081 12.894 19.8939 13.5798 19.8939 14.4227V17.2143H14.4712C12.9928 17.2143 11.7901 18.417 11.7901 19.8954C11.7901 21.3738 12.9928 22.5765 14.4712 22.5765H19.8939V25.368C19.8939 26.2109 19.2081 26.8967 18.3652 26.8967H3.63109C2.78823 26.8967 2.10244 26.2109 2.10244 25.368V22.2592C2.10244 21.941 1.84446 21.683 1.52622 21.683C1.20797 21.683 0.95 21.941 0.95 22.2592V25.368C0.95 26.8464 2.15269 28.0491 3.63109 28.0491H20.6806C22.159 28.0491 23.3616 26.8464 23.3616 25.368V14.4227C23.3616 12.9443 22.159 11.7416 20.6806 11.7416ZM8.32754 7.8564C9.39767 7.8564 10.2683 8.72705 10.2683 9.79718C10.2683 10.8673 9.39767 11.738 8.32754 11.738C7.25746 11.738 6.38675 10.8673 6.38675 9.79718C6.38675 8.72705 7.25741 7.8564 8.32754 7.8564ZM21.0463 14.4227C21.0463 13.855 20.8688 13.328 20.5666 12.894H20.6806C21.5234 12.894 22.2092 13.5798 22.2092 14.4227V17.2143H21.0463V14.4227ZM12.9425 19.8954C12.9425 19.0525 13.6283 18.3667 14.4712 18.3667H22.2092V21.424H14.4712C13.6283 21.424 12.9425 20.7382 12.9425 19.8954ZM20.6806 26.8967H20.5666C20.8688 26.4628 21.0463 25.9358 21.0463 25.368V22.5765H22.2092V25.368C22.2092 26.2109 21.5234 26.8967 20.6806 26.8967Z"
                      stroke="#221F20"
                      strokeOpacity="0.85"
                      strokeWidth={0.1}
                    />
                    <Path
                      d="M14.3309 19.3711C14.192 19.3711 14.0562 19.4274 13.9583 19.5253C13.8605 19.6232 13.8047 19.7589 13.8047 19.8973C13.8047 20.0357 13.8605 20.1715 13.9583 20.2693C14.0567 20.3672 14.192 20.4235 14.3309 20.4235C14.4693 20.4235 14.6045 20.3672 14.7029 20.2693C14.8008 20.1715 14.8571 20.0357 14.8571 19.8973C14.8571 19.7589 14.8008 19.6232 14.7029 19.5253C14.6051 19.4273 14.4693 19.3711 14.3309 19.3711Z"
                      fill="#221F20"
                      fillOpacity="0.85"
                      stroke="#221F20"
                      strokeOpacity="0.85"
                      strokeWidth={0.2}
                    />
                    <Path
                      d="M15.9807 8.24661C17.6588 8.24661 19.0239 6.88144 19.0239 5.20338C19.0239 3.52538 17.6588 2.16016 15.9807 2.16016C14.3027 2.16016 12.9375 3.52532 12.9375 5.20338C12.9375 6.88144 14.3027 8.24661 15.9807 8.24661ZM15.9807 3.21265C17.0784 3.21265 17.9715 4.10569 17.9715 5.20343C17.9715 6.30118 17.0785 7.19422 15.9807 7.19422C14.883 7.19422 13.9899 6.30118 13.9899 5.20343C13.9899 4.10569 14.883 3.21265 15.9807 3.21265Z"
                      fill="#428BC1"
                      stroke="#428BC1"
                      strokeWidth={0.2}
                    />
                    <Path
                      d="M10.8465 6.18751C11.1372 6.18751 11.3727 5.95192 11.3727 5.66129V1.52622C11.3727 1.23559 11.1372 1 10.8465 1C10.5559 1 10.3203 1.23559 10.3203 1.52622V5.66129C10.3203 5.95192 10.5559 6.18751 10.8465 6.18751Z"
                      fill="#428BC1"
                      stroke="#428BC1"
                      strokeWidth={0.2}
                    />
                    <Path
                      d="M8.31528 4.3678C8.60591 4.3678 8.8415 4.13221 8.8415 3.84158V1.52622C8.8415 1.23559 8.60591 1 8.31528 1C8.02465 1 7.78906 1.23559 7.78906 1.52622V3.84158C7.78906 4.13221 8.02465 4.3678 8.31528 4.3678Z"
                      fill="#428BC1"
                      stroke="#428BC1"
                      strokeWidth={0.2}
                    />
                    <Path
                      d="M1.52622 20.4235C1.66461 20.4235 1.80038 20.3672 1.89825 20.2693C1.99613 20.1715 2.05244 20.0357 2.05244 19.8973C2.05244 19.7589 1.99613 19.6232 1.89825 19.5253C1.80038 19.4274 1.66514 19.3711 1.52622 19.3711C1.38782 19.3711 1.25206 19.4274 1.15418 19.5253C1.05631 19.6232 1 19.7589 1 19.8973C1 20.0357 1.05631 20.1715 1.15418 20.2693C1.25206 20.3672 1.38777 20.4235 1.52622 20.4235Z"
                      fill="#221F20"
                      fillOpacity="0.85"
                    />
                    <Path
                      d="M1.52622 20.5235C1.69117 20.5235 1.85236 20.4567 1.96897 20.3401C2.08557 20.2235 2.15244 20.0623 2.15244 19.8973C2.15244 19.7324 2.08557 19.5712 1.96897 19.4546C1.8524 19.338 1.69175 19.2711 1.52622 19.2711C1.36127 19.2711 1.20008 19.338 1.08347 19.4546C0.966867 19.5712 0.9 19.7324 0.9 19.8973C0.9 20.0623 0.966867 20.2235 1.08347 20.3401C1.20007 20.4567 1.36121 20.5235 1.52622 20.5235Z"
                      stroke="#221F20"
                      strokeOpacity="0.85"
                      strokeWidth={0.2}
                    />
                  </Svg>

                  {getHorizontalPadding(10)}

                  <View
                    style={{
                      flex: 1,
                      width: '100%',
                      flexDirection: 'column',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        flex: 1,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                        paddingRight: widthPixel(5),
                      }}>
                      <Text
                        style={{
                          fontSize: fonts._12,
                          fontFamily: fonts.FONT_FAMILY.Regular,
                          color: '#000',
                        }}>
                        {`Pack of ${product?.node?.pack_of_product_number?.value} @ `}
                      </Text>

                      <Text
                        style={{
                          fontFamily: fonts.FONT_FAMILY.Regular,
                          textDecorationLine: 'line-through',
                          fontSize: fonts._12,
                          lineHeight: heightPixel(20),
                          color: '#505050',
                        }}>
                        {currencyHelper(
                          productData?.compareAtPriceRange?.minVariantPrice
                            ?.amount,
                          productData?.compareAtPriceRange?.minVariantPrice
                            ?.currencyCode,
                        )}{' '}
                      </Text>

                      <Text
                        style={{
                          fontFamily: fonts.FONT_FAMILY.Medium,
                          fontSize: fonts._14,
                          lineHeight: heightPixel(20),
                        }}>
                        {currencyHelper(
                          productData?.priceRange?.minVariantPrice?.amount,
                          productData?.priceRange?.minVariantPrice?.currencyCode,
                        )}{" "}
                      </Text>

                      <Text
                        style={{
                          color: '#428bc1',
                          fontFamily: fonts.FONT_FAMILY.SemiBold,
                          fontSize: fonts._12,
                          // lineHeight: 20,
                        }}>
                        {`Saved ${currencyHelper(
                          productData?.compareAtPriceRange?.minVariantPrice
                            ?.amount -
                          productData?.priceRange?.minVariantPrice?.amount,
                          productData?.priceRange?.minVariantPrice?.currencyCode,
                        )}`}
                      </Text>

                    </View>

                  </View>

                  <Pressable
                    onPress={() =>
                      props.onAction({
                        action: 'OPEN_PRODUCT',
                        params: {
                          productHandle: productData.handle,
                        },
                      })
                    }>
                    <LinearGradient
                      colors={['#221f20', '#505050']}
                      start={{ x: -0.1, y: 0 }}
                      end={{ x: 1.4, y: 1 }}
                      style={{
                        paddingHorizontal: widthPixel(10),
                        borderRadius: widthPixel(7),
                        paddingVertical: heightPixel(8),
                      }}>
                      <Text style={styles.packOfProductButtonText}>
                        VIEW PRODUCT
                      </Text>
                    </LinearGradient>
                  </Pressable>
                </View>
              </View>
            )}

          {/* Return Policy Section */}
          <View
            style={{
              marginVertical: heightPixel(24),
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            {isReturnable ? (
              // Returnable Product Icon - using original return icon
              <Svg width="30" height="30" viewBox="0 0 44 44" fill="none">
                <Path
                  d="M33.4033 16.4709V27.5445C33.4069 27.7295 33.3491 27.9105 33.2387 28.059C33.1284 28.2076 32.9718 28.3153 32.7937 28.3653L22.3297 32.0373C22.2263 32.087 22.1131 32.1129 21.9985 32.1129C21.8838 32.1129 21.7706 32.087 21.6673 32.0373L11.1553 28.3653C10.9856 28.3074 10.8392 28.1962 10.738 28.0482C10.6368 27.9003 10.5861 27.7236 10.5937 27.5445L10.5409 16.4709C10.5485 16.2816 10.6172 16.0999 10.7366 15.9529C10.8559 15.8058 11.0196 15.7013 11.2033 15.6549L21.7201 11.9829C21.8044 11.933 21.9005 11.9067 21.9985 11.9067C22.0964 11.9067 22.1925 11.933 22.2769 11.9829L32.8369 15.6549C33.0057 15.7125 33.1515 15.823 33.2527 15.9699C33.3538 16.1169 33.4049 16.2926 33.3985 16.4709H33.4033ZM12.0193 41.1717C11.8205 41.0605 11.6733 40.8757 11.6095 40.6571C11.5456 40.4386 11.5701 40.2036 11.6776 40.0029C11.7852 39.8022 11.9673 39.6518 12.1847 39.584C12.4021 39.5162 12.6375 39.5365 12.8401 39.6404C16.8909 41.7526 21.5457 42.4049 26.0211 41.4875C30.4965 40.5701 34.5192 38.1392 37.4124 34.6036C40.3057 31.0681 41.8927 26.6439 41.9065 22.0755C41.9202 17.5071 40.3599 13.0734 37.4881 9.52045C37.4644 9.47802 37.4258 9.44585 37.3798 9.43014C37.3339 9.41443 37.2837 9.41629 37.239 9.43536C37.1943 9.45443 37.1582 9.48936 37.1377 9.53343C37.1173 9.5775 37.1138 9.62759 37.1281 9.67405L37.2817 10.2885C37.5889 11.4117 35.9041 11.8197 35.5969 10.7685L34.3729 6.06925C34.33 5.9229 34.328 5.76761 34.3671 5.62021C34.4062 5.47281 34.4849 5.33893 34.5947 5.23309C34.7045 5.12725 34.8412 5.05349 34.9899 5.01982C35.1387 4.98614 35.2938 4.99383 35.4385 5.04205L40.0849 6.26605C40.1955 6.29757 40.2988 6.35057 40.389 6.42202C40.4791 6.49347 40.5543 6.58198 40.6102 6.68249C40.6662 6.783 40.7018 6.89355 40.715 7.00781C40.7282 7.12208 40.7188 7.23783 40.6873 7.34845C40.6557 7.45908 40.6027 7.56241 40.5313 7.65256C40.4598 7.7427 40.3713 7.81789 40.2708 7.87383C40.1703 7.92976 40.0598 7.96536 39.9455 7.97857C39.8312 7.99179 39.7155 7.98237 39.6049 7.95085L38.6881 7.69645C38.4817 7.64365 38.3281 7.85005 38.4817 7.99885C41.7155 11.8221 43.5226 16.6491 43.5945 21.6561C43.6664 26.6631 41.9987 31.54 38.876 35.4546C35.7533 39.3691 31.369 42.0789 26.4712 43.1215C21.5734 44.1641 16.4656 43.475 12.0193 41.1717ZM32.4865 3.10285C33.5041 3.66445 32.6401 5.19565 31.6705 4.63405C27.6391 2.39054 22.9531 1.61853 18.4151 2.45024C13.8771 3.28195 9.76941 5.66563 6.79571 9.19301C3.822 12.7204 2.16727 17.172 2.11494 21.7853C2.06261 26.3986 3.61593 30.8865 6.50886 34.4804C6.54084 34.5165 6.58416 34.5405 6.63165 34.5485C6.67914 34.5565 6.72794 34.548 6.76996 34.5245C6.81197 34.5009 6.84469 34.4638 6.86268 34.4191C6.88066 34.3744 6.88284 34.3249 6.86886 34.2789L6.71526 33.7173C6.40806 32.5941 8.09286 32.1861 8.40006 33.2373L9.62406 37.9317C9.66974 38.079 9.67395 38.2361 9.63624 38.3857C9.59853 38.5353 9.52035 38.6716 9.41027 38.7797C9.30019 38.8878 9.16247 38.9635 9.01221 38.9985C8.86195 39.0335 8.70496 39.0264 8.55846 38.9781L3.91206 37.7541C2.78886 37.4468 3.24966 35.7621 4.32006 36.0692L5.28006 36.3237C5.48646 36.3764 5.64006 36.1701 5.48646 35.9684C-1.75674 27.4965 -1.19514 14.6373 6.71526 6.72685C10.036 3.40832 14.3461 1.26005 18.995 0.606313C23.6439 -0.0474275 28.3793 0.828841 32.4865 3.10285ZM22.8385 20.9109V29.7429C22.8398 29.7788 22.8498 29.8139 22.8675 29.8452C22.8851 29.8765 22.9101 29.9031 22.9401 29.9228C22.9702 29.9425 23.0045 29.9548 23.0403 29.9585C23.076 29.9623 23.1122 29.9575 23.1457 29.9445L31.5649 26.9829C31.6177 26.9349 31.7185 26.8821 31.7185 26.7813V17.9541C31.7185 17.8485 31.5649 17.7477 31.4161 17.8005L22.9921 20.7093C22.8913 20.7573 22.8385 20.8101 22.8385 20.9109ZM21.1057 29.7429V20.9109C21.1048 20.8719 21.0953 20.8336 21.0779 20.7988C21.0604 20.7639 21.0355 20.7333 21.0049 20.7093L17.5825 19.5333C17.5488 19.5211 17.5128 19.517 17.4773 19.5215C17.4418 19.526 17.4079 19.5389 17.3784 19.559C17.3489 19.5792 17.3245 19.6061 17.3074 19.6375C17.2903 19.6689 17.281 19.7039 17.2801 19.7397V23.0037C17.2807 23.1179 17.2587 23.2312 17.2152 23.3369C17.1718 23.4426 17.1078 23.5386 17.027 23.6194C16.9462 23.7002 16.8502 23.7642 16.7445 23.8076C16.6388 23.8511 16.5255 23.8731 16.4113 23.8725C16.1828 23.8664 15.9653 23.7729 15.8036 23.6113C15.642 23.4496 15.5485 23.2322 15.5425 23.0037V18.9717C15.5416 18.9327 15.5321 18.8944 15.5147 18.8596C15.4972 18.8247 15.4723 18.7941 15.4417 18.7701L12.5617 17.8101C12.4129 17.7573 12.2593 17.8581 12.2593 17.9637V26.8005C12.2593 26.9013 12.3073 26.9541 12.4129 27.0021L20.8321 29.9637C20.9857 30.0165 21.0865 29.9109 21.0865 29.7621L21.1057 29.7429ZM14.6257 16.6773L16.8721 17.4405C16.8805 17.4523 16.8916 17.462 16.9046 17.4687C16.9175 17.4754 16.9319 17.4789 16.9465 17.4789C16.961 17.4789 16.9754 17.4754 16.9883 17.4687C17.0013 17.462 17.0124 17.4523 17.0209 17.4405L24.3217 14.8917C24.4225 14.8917 24.4753 14.7909 24.4753 14.6853C24.472 14.6456 24.4547 14.6084 24.4266 14.5803C24.3985 14.5522 24.3613 14.5349 24.3217 14.5317L22.0753 13.7157H21.9217L14.6257 16.2693C14.5808 16.2813 14.5411 16.3078 14.5128 16.3447C14.4845 16.3816 14.4691 16.4268 14.4691 16.4733C14.4691 16.5197 14.4845 16.5649 14.5128 16.6018C14.5411 16.6387 14.5808 16.6652 14.6257 16.6773ZM20.1361 18.5973L21.9217 19.2069H22.0753L29.3713 16.6581C29.4162 16.646 29.4558 16.6195 29.4841 16.5826C29.5124 16.5457 29.5278 16.5005 29.5278 16.4541C29.5278 16.4076 29.5124 16.3624 29.4841 16.3255C29.4558 16.2886 29.4162 16.2621 29.3713 16.2501L27.5377 15.5877H27.3841L20.1361 18.2085C19.9297 18.2565 19.9297 18.5157 20.1361 18.6165V18.5973ZM29.5249 24.4869C29.7379 24.425 29.9665 24.4475 30.1634 24.5497C30.3604 24.6518 30.5104 24.8257 30.5826 25.0355C30.6548 25.2452 30.6435 25.4747 30.5512 25.6764C30.4588 25.8781 30.2924 26.0365 30.0865 26.1189L27.2305 27.1365C27.0174 27.1983 26.7888 27.1758 26.5919 27.0737C26.395 26.9715 26.2449 26.7976 26.1727 26.5878C26.1005 26.3781 26.1118 26.1486 26.2041 25.9469C26.2965 25.7452 26.4629 25.5868 26.6689 25.5045L29.5249 24.4869Z"
                  fill="black"
                />
              </Svg>
            ) : (
              // Non-returnable Product Icon
              <Svg width="30" height="30" viewBox="0 0 24 24" fill="none">
                <Path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm0-4h-2V7h2v8z"
                  fill="black"
                />
              </Svg>
            )}
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  paddingLeft: widthPixel(5),
                  fontSize: fonts._14,
                  color: '#221f20',
                  fontFamily: fonts.FONT_FAMILY.SemiBold,
                }}>
                {isReturnable ? 'Easy 15 days return policy' : 'This product is non-returnable.'}
              </Text>
              <Text
                style={{
                  color: '#428bc1',
                  alignItems: 'center',
                  textAlign: 'center',
                  marginRight: widthPixel(5),
                  fontSize: fonts._14,
                  fontFamily: fonts.FONT_FAMILY.Regular,
                }}
                onPress={openModal}>
                {' '}
                Know more →
              </Text>
            </View>
          </View>

          {/* delivery Details */}
          {/* ************ ESTIMATED DELIVERY DATE ************* */}
          <Edd
            variants={product?.node?.variants.edges}
            selectedVariant={selectedVariant}
          />

          {/* product description */}
          {getVerticalPadding(24)}
          {product?.node?.descriptionHtml && (
            <ProductDescription
              descriptionImage={data?.product?.product_description_image_dev ?? data?.product?.product_description_image_prod}
              description={product?.node?.descriptionHtml}
            />
          )}


          {data?.product?.disclaimer_text?.value && (
            <Text>Disclaimer: {data?.product?.disclaimer_text?.value}</Text>
          )}

          {/* ideal for */}
          {productIdealForImage.length > 0 &&
            data?.product?.product_is_ideal_for_sub_text.value.length > 0 && (
              <View>
                <Text
                  style={{
                    // paddingVertical: heightPixel(10),
                    fontSize: fonts._16,
                    color: '#221f20',
                    fontFamily: fonts.FONT_FAMILY.SemiBold,
                    // marginHorizontal: widthPixel(5),
                  }}>
                  Ideal for
                </Text>

                <View>
                  <View
                    style={{
                      display: 'flex',
                      // justifyContent: 'space-between',
                      gap: widthPixel(6),
                      // alignSelf: "center",
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'row',
                      marginTop: heightPixel(4),
                    }}>
                    {productIdealForImage?.map((item, index) => {
                      return (
                        <View
                          style={{
                            // display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            // flex: 1,
                            height: heightPixel(100),
                            maxWidth: widthPixel(110),
                            // backgroundColor: "red",
                            marginVertical: widthPixel(10),
                            borderWidth: 1,
                            borderColor: 'rgba(34,31,32,.09)',
                            borderRadius: widthPixel(10),
                          }}>
                          <View
                            style={{
                              width: widthPixel(36),
                              height: heightPixel(36),
                              alignItems: 'center',
                            }}>
                            {item.image.src.includes('.svg') ? (
                              <SvgUri
                                width="100%"
                                height="100%"
                                uri={item.image.src}
                              />
                            ) : (
                              <Image
                                source={{ uri: item.image.src }}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  resizeMode: 'cover',
                                }}
                              />
                            )}
                          </View>

                          <View style={{ marginTop: 4 }}>
                            <Text
                              style={{
                                textAlign: 'center',
                                fontSize: fonts._15,
                                color: '#221f20',
                                fontFamily: fonts.FONT_FAMILY.Bold,
                                width: widthPixel(90),
                              }}>
                              {
                                JSON.parse(
                                  data?.product?.product_is_ideal_for_sub_text
                                    .value,
                                )[index]
                              }
                            </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              </View>
            )}

          {

            rawValue &&
            <View style={{
              height: heightPixel(550),
              width: '100%',
              flexDirection: 'row',
              justifyContent: "space-between",
              alignItems: 'center',
              borderWidth: 1,
              paddingHorizontal: widthPixel(10),
              paddingVertical: heightPixel(10),
              borderRadius: widthPixel(8),
              borderColor: '#d9d9d9',
              marginTop: heightPixel(20)
            }}>

              <View style={{
                flex: 1,

              }} >

                <MaskedView
                  style={{ width: widthPixel(50), height: heightPixel(550) }}
                  maskElement={
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 44,
                          color: 'black',
                          transform: [{ rotate: '-90deg' }],
                          width: heightPixel(550),
                          textAlign: 'center',
                          includeFontPadding: false,
                          textAlignVertical: 'center',
                          fontFamily: "Jost-Bold",
                          letterSpacing: -3
                        }}
                        numberOfLines={1}
                        ellipsizeMode="clip"
                      >
                        SHOP THE LOOK
                      </Text>
                    </View>
                  }
                >
                  <LinearGradient
                    colors={['#000000', '#b0afaf', '#000000']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={{
                      flex: 1,
                    }}
                  />
                </MaskedView>

              </View>

              {/* <ShopifyImage
              source={{ uri: product?.product?.media?.edges?.[3]?.node?.image?.url }}
              style={{ width: widthPixel(250), height: "100%", borderRadius: widthPixel(8) }}
              resizeMode="cover"
            /> */}

              <ImageBackground
                source={{ uri: product?.product?.media?.edges?.[3]?.node?.image?.url }}
                style={{ width: widthPixel(250), height: "100%", borderRadius: widthPixel(8), overflow: "hidden" }}
                resizeMode="cover">

                <Pressable
                  style={[
                    styles.svgContainer,
                    { top: `${23}%`, left: `${50}%` },
                  ]}
                  onPress={() => {
                    bottomSheetRef.current.open();
                    firstClick()
                  }}>

                  <Animated.View
                    style={[
                      styles.svgContainer,
                      { top: `${23}%`, left: `${50}%` },
                    ]}>
                    <Animated.View
                      style={[
                        styles.blinkingCircle,
                        // { opacity: backgroundOpacity }
                        {
                          opacity: backgroundOpacity,
                          transform: [{ scale: backgroundScale }],
                        },
                      ]}
                    />

                    <View style={styles.svgWrapper}>

                      <Svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 34 34" fill="none">
                        <Circle opacity="0.5" cx="17" cy="17" r="14" fill="#FFFCFC"></Circle>
                        <Circle cx="17" cy="17" r="11" fill="url(#paint0_linear_1240_37410)"></Circle>
                        <Path d="M13 17H21M17 21L17 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></Path>
                        <Defs>
                          <LinearGradientSvg id="paint0_linear_1240_37410" x1="35.5952" y1="32.8" x2="-5.85342" y2="28.6176" gradientUnits="userSpaceOnUse">
                            <Stop stop-color="#221F20"></Stop>
                            <Stop offset="1" stop-color="#505050"></Stop>
                          </LinearGradientSvg>
                        </Defs>
                      </Svg>
                    </View>
                  </Animated.View>

                </Pressable>


                <Pressable
                  style={[
                    styles.svgContainer,
                    { top: `${60}%`, left: `${40}%` },
                  ]}
                  onPress={() => {
                    bottomSheetRef.current.open();
                    secondClick()
                  }}>

                  <Animated.View
                    style={[
                      styles.svgContainer,
                      { top: `${60}%`, left: `${40}%` },
                    ]}>
                    <Animated.View
                      style={[
                        styles.blinkingCircle,
                        {
                          opacity: backgroundOpacity,
                          transform: [{ scale: backgroundScale }],
                        },
                      ]}
                    />

                    <View style={styles.svgWrapper}>

                      {/* <Svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="32"
                      height="33"
                      viewBox="0 0 32 33"
                      fill="none">
                      <Defs>
                        <LinearGradientSvg
                          id="paint0_linear"
                          x1="33.2871"
                          y1="31.6612"
                          x2="-5.47603"
                          y2="27.7498"
                          gradientUnits="userSpaceOnUse">
                          <Stop stopColor="#221F20" />
                          <Stop offset="1" stopColor="#505050" />
                        </LinearGradientSvg>
                      </Defs>
                      <Circle
                        cx="15.8967"
                        cy="16.8849"
                        r="10.2873"
                        fill="url(#paint0_linear)"
                      />
                      <Path
                        d="M12.1562 16.8854H19.6379"
                        stroke="white"
                        strokeWidth="1.87"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <Path
                        d="M15.8971 20.6262L15.8971 13.1445"
                        stroke="white"
                        strokeWidth="1.87"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </Svg> */}

                      <Svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 34 34" fill="none">
                        <Circle opacity="0.5" cx="17" cy="17" r="14" fill="#FFFCFC"></Circle>
                        <Circle cx="17" cy="17" r="11" fill="url(#paint0_linear_1240_37410)"></Circle>
                        <Path d="M13 17H21M17 21L17 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></Path>
                        <Defs>
                          <LinearGradientSvg id="paint0_linear_1240_37410" x1="35.5952" y1="32.8" x2="-5.85342" y2="28.6176" gradientUnits="userSpaceOnUse">
                            <Stop stop-color="#221F20"></Stop>
                            <Stop offset="1" stop-color="#505050"></Stop>
                          </LinearGradientSvg>
                        </Defs>
                      </Svg>
                    </View>
                  </Animated.View>
                </Pressable>


              </ImageBackground>

              <RBSheet
                ref={bottomSheetRef}
                openDuration={100}
                closeDuration={0}
                customStyles={{
                  container: styles.bottomModalContent,
                }}
                animationType="slide"
                useNativeDriver
                onOpen={{}}
                onClose={{}}>

                {

                  isFetching ?

                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }} >

                      <Text>Loading....</Text>

                    </View>

                    :

                    <>

                      <View style={styles.bottomModalHeader}>
                        <Text style={styles.bottomModalHeading}>SHOP THE LOOK</Text>
                        <Pressable
                          onPress={() => {
                            bottomSheetRef.current.close();
                          }}
                          style={styles.bottomCloseButton}>
                          <Svg width={20} height={20} viewBox="0 0 20 20" fill="none">
                            <Path
                              d="M14.875 14.875L5.125 5.125M14.875 5.125L5.125 14.875"
                              stroke="#221F20"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </Svg>
                        </Pressable>
                      </View>


                      <View
                        style={{
                          flexDirection: 'row',
                          // paddingRight: 20,
                          // paddingLeft: 20,
                          paddingHorizontal: widthPixel(30),
                          gap: widthPixel(10),
                          marginLeft: -10,
                          // width: width,
                          // backgroundColor: 'yellow',
                        }}>
                        {productList?.slice(0, 2).map(
                          (value, index) => {
                            function openProduct() {
                              bottomSheetRef.current.close();
                              props.onAction({
                                action: 'OPEN_PRODUCT',
                                params: {
                                  productHandle: value?.node?.handle,
                                },
                              });
                            }

                            return (
                              <PrimeSelectionProductCard
                                key={value?.node?.handle}
                                onClick={openProduct}
                                product={
                                  productList?.node
                                }
                                productNode={value?.node}
                                title={value?.node?.title}
                                imageUrl={value?.node?.images}
                                regularPrice={value?.node?.priceRange?.minVariantPrice}
                                productVariantDetails={
                                  productList?.node
                                }
                                props={props}
                                variationOptions={value?.node?.options}
                                openModal={(variant) =>
                                  openVariantModal(variant, index, value)
                                }
                                firstImage={true}
                              />
                            );
                          },
                        )}
                      </View>

                    </>

                }

              </RBSheet>

              {isVariantModalVisible && (
                <ProductModal
                  modalVisible={isVariantModalVisible}
                  toggleModal={() => {
                    setVariantModalVisible(!isVariantModalVisible);
                  }}
                  handle={selectedProductData?.productNode?.handle}
                  // productVariantDetails={allVariants?.[activeIndex]}
                  productVariantDetails={selectedProductData?.productNode?.color_variant?.references?.nodes.length > 0 ? moveProductToFirst(
                    selectedProductData?.productNode?.color_variant?.references,
                    selectedProductData?.productNode?.id,
                  ) : allVariants}
                  props={props}
                />
              )}

            </View>

          }





          {data?.product?.washing_instructions_text?.value?.length > 0 &&
            productWashingInstructionImage.length > 0 && (
              <>
                <Text
                  style={{
                    fontSize: fonts._16,
                    textAlign: 'left',
                    color: '#221f20',
                    lineHeight: heightPixel(23),
                    fontFamily: fonts.FONT_FAMILY.SemiBold,
                    marginBottom: heightPixel(5),
                    // marginHorizontal: widthPixel(10),
                    marginVertical: heightPixel(20),
                  }}>
                  Washing Instructions
                </Text>
                <View style={styles.washingContainer}>
                  <View style={styles.bottomSection}>
                    {productWashingInstructionImage.map((value, index) => {
                      return (
                        <View
                          key={index} // Adding a key for list items
                          style={styles.cardContainer}
                        // style={{
                        //   display: 'flex',
                        //   justifyContent: 'center',
                        //   alignItems: 'center',
                        //   // width: "50%",
                        //   padding: 10
                        // }}
                        >
                          <View
                            style={{
                              width: widthPixel(36),
                              height: widthPixel(36),
                              alignItems: 'center',
                            }}>
                            {value.image.src.includes('.svg') ? (
                              <SvgUri
                                width="100%"
                                height="100%"
                                uri={value.image.src}
                              />
                            ) : (
                              <Image
                                source={{ uri: value.image.src }}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  resizeMode: 'cover',
                                }}
                              />
                            )}
                          </View>
                          <View style={{ marginTop: heightPixel(4) }}>
                            <Text
                              style={{
                                textAlign: 'center',
                                fontSize: fonts._15,
                                color: '#221f20',
                                fontFamily: fonts.FONT_FAMILY.Regular,
                                letterSpacing: 0,
                              }}>
                              {
                                JSON.parse(
                                  data?.product?.washing_instructions_text?.value,
                                )[index]
                              }
                            </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              </>
            )}

          {/* Manufacturing Details */}
          {data?.product?.style_note?.value && (
            <View style={{ marginBottom: heightPixel(20), borderWidth: 0 }}>
              <Text
                style={{
                  fontSize: fonts._16,
                  textAlign: 'left',
                  color: '#221f20',
                  lineHeight: heightPixel(23),
                  fontFamily: fonts.FONT_FAMILY.SemiBold,
                  marginBottom: heightPixel(20),
                  // marginHorizontal: widthPixel(5),
                  marginVertical: heightPixel(10),
                }}>
                Manufacturing Details
              </Text>
              <View
                style={{
                  borderWidth: 0,
                  overflow: 'hidden',
                }}>
                <HTML
                  source={{
                    html: renderContent(data?.product?.style_note?.value),
                  }}
                  contentWidth={Dimensions.get('window').width}
                />

                <View
                  style={{
                    flexDirection: 'row',
                    width: '50%',
                    alignItems: 'center',
                    marginTop: heightPixel(10),
                  }}>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      marginRight: widthPixel(3),
                      fontFamily: 'Jost-Regular',
                    }}>
                    Country Of Origin{' '}
                  </Text>
                  <View
                    style={{
                      borderWidth: 1,
                      borderColor: '#d9d9d9',
                      paddingHorizontal: widthPixel(6),
                      borderRadius: widthPixel(20),
                      height: heightPixel(30),
                      width: widthPixel(65),
                    }}>
                    <Image
                      source={{ uri: countryImage }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                </View>
              </View>
            </View>
          )}
          {getVerticalPadding(50)}
        </View>
      </ScrollView >

      <View>
        <Modal
          transparent={true}
          visible={modalVisible}
          onRequestClose={closeModal}>
          <View style={[styles.modalContainer, { paddingTop: insets.top }]}>
            <View style={styles.modalContent}>
              {/* return n close */}
              <View style={styles.modalHeader}>
                <Text style={styles.modalHeaderText}>Return Policy</Text>
                <TouchableOpacity
                  onPress={closeModal}
                  style={styles.closeButton}>
                  <Svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                    <Path
                      d="M13.2983 0.710224C13.2058 0.61752 13.0959 0.543973 12.9749 0.493791C12.8539 0.44361 12.7242 0.417779 12.5933 0.417779C12.4623 0.417779 12.3326 0.44361 12.2116 0.493791C12.0907 0.543973 11.9808 0.61752 11.8883 0.710224L6.99827 5.59022L2.10827 0.700223C2.01569 0.607642 1.90578 0.534202 1.78481 0.484097C1.66385 0.433992 1.5342 0.408203 1.40327 0.408203C1.27234 0.408203 1.14269 0.433992 1.02173 0.484097C0.900763 0.534202 0.790852 0.607642 0.69827 0.700223C0.605688 0.792805 0.532248 0.902716 0.482143 1.02368C0.432038 1.14464 0.40625 1.27429 0.40625 1.40522C0.40625 1.53615 0.432038 1.6658 0.482143 1.78677C0.532248 1.90773 0.605688 2.01764 0.69827 2.11022L5.58827 7.00022L0.69827 11.8902C0.605688 11.9828 0.532248 12.0927 0.482143 12.2137C0.432038 12.3346 0.40625 12.4643 0.40625 12.5952C0.40625 12.7262 0.432038 12.8558 0.482143 12.9768C0.532248 13.0977 0.605688 13.2076 0.69827 13.3002C0.790852 13.3928 0.900763 13.4662 1.02173 13.5163C1.14269 13.5665 1.27234 13.5922 1.40327 13.5922C1.5342 13.5922 1.66385 13.5665 1.78481 13.5163C1.90578 13.4662 2.01569 13.3928 2.10827 13.3002L6.99827 8.41022L11.8883 13.3002C11.9809 13.3928 12.0908 13.4662 12.2117 13.5163C12.3327 13.5665 12.4623 13.5922 12.5933 13.5922C12.7242 13.5922 12.8538 13.5665 12.9748 13.5163C13.0958 13.4662 13.2057 13.3928 13.2983 13.3002C13.3909 13.2076 13.4643 13.0977 13.5144 12.9768C13.5645 12.8558 13.5903 12.7262 13.5903 12.5952C13.5903 12.4643 13.5645 12.3346 13.5144 12.2137C13.4643 12.0927 13.3909 11.9828 13.2983 11.8902L8.40827 7.00022L13.2983 2.11022C13.6783 1.73022 13.6783 1.09022 13.2983 0.710224Z"
                      fill="black"
                    />
                  </Svg>
                </TouchableOpacity>
              </View>

              {/* Scrollable Content */}
              <ScrollView
                style={{ maxHeight: heightPixel(500) }}
                showsVerticalScrollIndicator={true}
                contentContainerStyle={{ paddingBottom: heightPixel(20) }}
              >
                {/* Original return policy content */}
                <View style={styles.PolicyHeader}>
                  <TouchableOpacity>
                    <Svg width="50" height="50" viewBox="0 0 44 44" fill="none">
                      <Path
                        d="M33.4033 16.4709V27.5445C33.4069 27.7295 33.3491 27.9105 33.2387 28.059C33.1284 28.2076 32.9718 28.3153 32.7937 28.3653L22.3297 32.0373C22.2263 32.087 22.1131 32.1129 21.9985 32.1129C21.8838 32.1129 21.7706 32.087 21.6673 32.0373L11.1553 28.3653C10.9856 28.3074 10.8392 28.1962 10.738 28.0482C10.6368 27.9003 10.5861 27.7236 10.5937 27.5445L10.5409 16.4709C10.5485 16.2816 10.6172 16.0999 10.7366 15.9529C10.8559 15.8058 11.0196 15.7013 11.2033 15.6549L21.7201 11.9829C21.8044 11.933 21.9005 11.9067 21.9985 11.9067C22.0964 11.9067 22.1925 11.933 22.2769 11.9829L32.8369 15.6549C33.0057 15.7125 33.1515 15.823 33.2527 15.9699C33.3538 16.1169 33.4049 16.2926 33.3985 16.4709H33.4033ZM12.0193 41.1717C11.8205 41.0605 11.6733 40.8757 11.6095 40.6571C11.5456 40.4386 11.5701 40.2036 11.6776 40.0029C11.7852 39.8022 11.9673 39.6518 12.1847 39.584C12.4021 39.5162 12.6375 39.5365 12.8401 39.6404C16.8909 41.7526 21.5457 42.4049 26.0211 41.4875C30.4965 40.5701 34.5192 38.1392 37.4124 34.6036C40.3057 31.0681 41.8927 26.6439 41.9065 22.0755C41.9202 17.5071 40.3599 13.0734 37.4881 9.52045C37.4644 9.47802 37.4258 9.44585 37.3798 9.43014C37.3339 9.41443 37.2837 9.41629 37.239 9.43536C37.1943 9.45443 37.1582 9.48936 37.1377 9.53343C37.1173 9.5775 37.1138 9.62759 37.1281 9.67405L37.2817 10.2885C37.5889 11.4117 35.9041 11.8197 35.5969 10.7685L34.3729 6.06925C34.33 5.9229 34.328 5.76761 34.3671 5.62021C34.4062 5.47281 34.4849 5.33893 34.5947 5.23309C34.7045 5.12725 34.8412 5.05349 34.9899 5.01982C35.1387 4.98614 35.2938 4.99383 35.4385 5.04205L40.0849 6.26605C40.1955 6.29757 40.2988 6.35057 40.389 6.42202C40.4791 6.49347 40.5543 6.58198 40.6102 6.68249C40.6662 6.783 40.7018 6.89355 40.715 7.00781C40.7282 7.12208 40.7188 7.23783 40.6873 7.34845C40.6557 7.45908 40.6027 7.56241 40.5313 7.65256C40.4598 7.7427 40.3713 7.81789 40.2708 7.87383C40.1703 7.92976 40.0598 7.96536 39.9455 7.97857C39.8312 7.99179 39.7155 7.98237 39.6049 7.95085L38.6881 7.69645C38.4817 7.64365 38.3281 7.85005 38.4817 7.99885C41.7155 11.8221 43.5226 16.6491 43.5945 21.6561C43.6664 26.6631 41.9987 31.54 38.876 35.4546C35.7533 39.3691 31.369 42.0789 26.4712 43.1215C21.5734 44.1641 16.4656 43.475 12.0193 41.1717ZM32.4865 3.10285C33.5041 3.66445 32.6401 5.19565 31.6705 4.63405C27.6391 2.39054 22.9531 1.61853 18.4151 2.45024C13.8771 3.28195 9.76941 5.66563 6.79571 9.19301C3.822 12.7204 2.16727 17.172 2.11494 21.7853C2.06261 26.3986 3.61593 30.8865 6.50886 34.4804C6.54084 34.5165 6.58416 34.5405 6.63165 34.5485C6.67914 34.5565 6.72794 34.548 6.76996 34.5245C6.81197 34.5009 6.84469 34.4638 6.86268 34.4191C6.88066 34.3744 6.88284 34.3249 6.86886 34.2789L6.71526 33.7173C6.40806 32.5941 8.09286 32.1861 8.40006 33.2373L9.62406 37.9317C9.66974 38.079 9.67395 38.2361 9.63624 38.3857C9.59853 38.5353 9.52035 38.6716 9.41027 38.7797C9.30019 38.8878 9.16247 38.9635 9.01221 38.9985C8.86195 39.0335 8.70496 39.0264 8.55846 38.9781L3.91206 37.7541C2.78886 37.4468 3.24966 35.7621 4.32006 36.0692L5.28006 36.3237C5.48646 36.3764 5.64006 36.1701 5.48646 35.9684C-1.75674 27.4965 -1.19514 14.6373 6.71526 6.72685C10.036 3.40832 14.3461 1.26005 18.995 0.606313C23.6439 -0.0474275 28.3793 0.828841 32.4865 3.10285ZM22.8385 20.9109V29.7429C22.8398 29.7788 22.8498 29.8139 22.8675 29.8452C22.8851 29.8765 22.9101 29.9031 22.9401 29.9228C22.9702 29.9425 23.0045 29.9548 23.0403 29.9585C23.076 29.9623 23.1122 29.9575 23.1457 29.9445L31.5649 26.9829C31.6177 26.9349 31.7185 26.8821 31.7185 26.7813V17.9541C31.7185 17.8485 31.5649 17.7477 31.4161 17.8005L22.9921 20.7093C22.8913 20.7573 22.8385 20.8101 22.8385 20.9109ZM21.1057 29.7429V20.9109C21.1048 20.8719 21.0953 20.8336 21.0779 20.7988C21.0604 20.7639 21.0355 20.7333 21.0049 20.7093L17.5825 19.5333C17.5488 19.5211 17.5128 19.517 17.4773 19.5215C17.4418 19.526 17.4079 19.5389 17.3784 19.559C17.3489 19.5792 17.3245 19.6061 17.3074 19.6375C17.2903 19.6689 17.281 19.7039 17.2801 19.7397V23.0037C17.2807 23.1179 17.2587 23.2312 17.2152 23.3369C17.1718 23.4426 17.1078 23.5386 17.027 23.6194C16.9462 23.7002 16.8502 23.7642 16.7445 23.8076C16.6388 23.8511 16.5255 23.8731 16.4113 23.8725C16.1828 23.8664 15.9653 23.7729 15.8036 23.6113C15.642 23.4496 15.5485 23.2322 15.5425 23.0037V18.9717C15.5416 18.9327 15.5321 18.8944 15.5147 18.8596C15.4972 18.8247 15.4723 18.7941 15.4417 18.7701L12.5617 17.8101C12.4129 17.7573 12.2593 17.8581 12.2593 17.9637V26.8005C12.2593 26.9013 12.3073 26.9541 12.4129 27.0021L20.8321 29.9637C20.9857 30.0165 21.0865 29.9109 21.0865 29.7621L21.1057 29.7429ZM14.6257 16.6773L16.8721 17.4405C16.8805 17.4523 16.8916 17.462 16.9046 17.4687C16.9175 17.4754 16.9319 17.4789 16.9465 17.4789C16.961 17.4789 16.9754 17.4754 16.9883 17.4687C17.0013 17.462 17.0124 17.4523 17.0209 17.4405L24.3217 14.8917C24.4225 14.8917 24.4753 14.7909 24.4753 14.6853C24.472 14.6456 24.4547 14.6084 24.4266 14.5803C24.3985 14.5522 24.3613 14.5349 24.3217 14.5317L22.0753 13.7157H21.9217L14.6257 16.2693C14.5808 16.2813 14.5411 16.3078 14.5128 16.3447C14.4845 16.3816 14.4691 16.4268 14.4691 16.4733C14.4691 16.5197 14.4845 16.5649 14.5128 16.6018C14.5411 16.6387 14.5808 16.6652 14.6257 16.6773ZM20.1361 18.5973L21.9217 19.2069H22.0753L29.3713 16.6581C29.4162 16.646 29.4558 16.6195 29.4841 16.5826C29.5124 16.5457 29.5278 16.5005 29.5278 16.4541C29.5278 16.4076 29.5124 16.3624 29.4841 16.3255C29.4558 16.2886 29.4162 16.2621 29.3713 16.2501L27.5377 15.5877H27.3841L20.1361 18.2085C19.9297 18.2565 19.9297 18.5157 20.1361 18.6165V18.5973ZM29.5249 24.4869C29.7379 24.425 29.9665 24.4475 30.1634 24.5497C30.3604 24.6518 30.5104 24.8257 30.5826 25.0355C30.6548 25.2452 30.6435 25.4747 30.5512 25.6764C30.4588 25.8781 30.2924 26.0365 30.0865 26.1189L27.2305 27.1365C27.0174 27.1983 26.7888 27.1758 26.5919 27.0737C26.395 26.9715 26.2449 26.7976 26.1727 26.5878C26.1005 26.3781 26.1118 26.1486 26.2041 25.9469C26.2965 25.7452 26.4629 25.5868 26.6689 25.5045L29.5249 24.4869Z"
                        fill="black"
                      />
                    </Svg>
                  </TouchableOpacity>

                  <View
                    style={{
                      width: '83%',
                      paddingHorizontal: widthPixel(10),
                      marginBottom: heightPixel(10),
                      borderWidth: 0,
                    }}>
                    {returnPolicyData?.children?.map((item, index) => (
                      <React.Fragment key={index}>
                        <RenderRichText node={item.children} />
                      </React.Fragment>
                    ))}
                  </View>
                </View>

                {/* Divider */}
                <View style={{
                  height: 1,
                  backgroundColor: '#e0e0e0',
                  marginVertical: heightPixel(20),
                  marginHorizontal: widthPixel(20)
                }} />

                {/* Easy Returns Section */}
                <View style={{ paddingHorizontal: widthPixel(20) }}>
                  <Text style={{
                    fontSize: fonts._18,
                    fontFamily: fonts.FONT_FAMILY.SemiBold,
                    color: '#221f20',
                    marginBottom: heightPixel(20)
                  }}>
                    Easy Returns
                  </Text>

                  {/* Step 1 */}
                  <View style={styles.returnStepContainer}>
                    <View style={styles.stepNumberContainer}>
                      <Text style={styles.stepNumber}>1</Text>
                    </View>
                    <View style={styles.stepIconContainer}>
                      <Svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <Path
                          d="M8 8H32V24H8V8ZM10 10V22H30V10H10ZM12 12H28V20H12V12ZM14 14V18H26V14H14Z"
                          fill="#221f20"
                        />
                        <Path
                          d="M16 26H24V28H16V26ZM18 30H22V32H18V30Z"
                          fill="#221f20"
                        />
                      </Svg>
                    </View>
                    <View style={styles.stepTextContainer}>
                      <Text style={styles.stepTitle}>Go to My Order {'>'} </Text>
                      <Text style={styles.stepDescription}>Return and select item/s to be returned</Text>
                    </View>
                  </View>

                  {/* Step 2 */}
                  <View style={styles.returnStepContainer}>
                    <View style={styles.stepNumberContainer}>
                      <Text style={styles.stepNumber}>2</Text>
                    </View>
                    <View style={styles.stepIconContainer}>
                      <Svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <Path
                          d="M20 4C18.9 4 18 4.9 18 6V8H14C12.9 8 12 8.9 12 10V32C12 33.1 12.9 34 14 34H26C27.1 34 28 33.1 28 32V10C28 8.9 27.1 8 26 8H22V6C22 4.9 21.1 4 20 4ZM20 6H20V8H20V6ZM14 10H26V32H14V10ZM16 12V30H24V12H16ZM18 14H22V16H18V14ZM18 18H22V20H18V18ZM18 22H22V24H18V22Z"
                          fill="#221f20"
                        />
                      </Svg>
                    </View>
                    <View style={styles.stepTextContainer}>
                      <Text style={styles.stepDescription}>Delivery agent will pick up the product</Text>
                    </View>
                  </View>

                  {/* Step 3 */}
                  <View style={styles.returnStepContainer}>
                    <View style={styles.stepNumberContainer}>
                      <Text style={styles.stepNumber}>3</Text>
                    </View>
                    <View style={styles.stepIconContainer}>
                      <Svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <Path
                          d="M8 12C6.9 12 6 12.9 6 14V26C6 27.1 6.9 28 8 28H32C33.1 28 34 27.1 34 26V14C34 12.9 33.1 12 32 12H8ZM8 14H32V26H8V14ZM10 16V24H30V16H10ZM12 18H28V22H12V18Z"
                          fill="#221f20"
                        />
                        <Path
                          d="M16 8H24V10H16V8ZM18 30H22V32H18V30Z"
                          fill="#221f20"
                        />
                      </Svg>
                    </View>
                    <View style={styles.stepTextContainer}>
                      <Text style={styles.stepDescription}>Refund gets queued for processing after successful pick-up</Text>
                    </View>
                  </View>
                </View>

                {/* Divider */}
                <View style={{
                  height: 1,
                  backgroundColor: '#e0e0e0',
                  marginVertical: heightPixel(20),
                  marginHorizontal: widthPixel(20)
                }} />

                {/* Info section with link */}
                <View style={{
                  paddingHorizontal: widthPixel(20),
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  marginBottom: heightPixel(10)
                }}>
                  <View style={{
                    width: widthPixel(24),
                    height: widthPixel(24),
                    borderRadius: widthPixel(12),
                    backgroundColor: '#e8f4fd',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: widthPixel(12),
                    marginTop: heightPixel(2),
                  }}>
                    <Text style={{
                      color: '#428bc1',
                      fontSize: fonts._14,
                      fontFamily: fonts.FONT_FAMILY.SemiBold,
                    }}>
                      i
                    </Text>
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: fonts._14,
                      color: '#221f20',
                      fontFamily: fonts.FONT_FAMILY.Regular,
                      lineHeight: heightPixel(20),
                    }}>
                      For more details, please refer to our{' '}
                      <Text
                        style={{
                          color: '#428bc1',
                          textDecorationLine: 'underline',
                        }}
                        onPress={() => {
                          closeModal();
                          props.onAction({
                            action: 'OPEN_WEBVIEW',
                            params: {
                              url: 'https://www.jockey.in/pages/return-policy',
                              title: 'Return Policy',
                              replacePage: false,
                            },
                          });
                        }}>
                        Return & Refund Policy
                      </Text>
                    </Text>
                  </View>
                </View>
              </ScrollView>

              {/* 2nd Policy */}
              <View style={styles.PolicyHeader}>
                <Svg width="40" height="40" viewBox="0 0 38 38" fill="none">
                  <Path
                    d="M32.3016 5.69961C25.0016 -1.60039 13.0016 -1.60039 5.70156 5.69961C-1.59844 12.9996 -1.59844 24.9996 5.70156 32.2996C9.40156 35.9996 14.2016 37.7996 19.0016 37.7996C23.8016 37.7996 28.6016 35.9996 32.3016 32.2996C39.6016 24.9996 39.6016 12.9996 32.3016 5.69961ZM30.9016 30.8996C24.4016 37.3996 13.7016 37.3996 7.20156 30.8996C0.701563 24.3996 0.701563 13.6996 7.20156 7.19961C10.5016 3.89961 14.8016 2.29961 19.1016 2.29961C23.4016 2.29961 27.7016 3.89961 31.0016 7.19961C37.4016 13.6996 37.4016 24.2996 30.9016 30.8996ZM19.6016 16.3996C19.8016 16.5996 20.0016 16.8996 20.0016 17.2996V26.8996C20.0016 27.2996 19.9016 27.5996 19.6016 27.7996C19.3016 27.9996 19.1016 28.0996 18.7016 28.0996C18.4016 28.0996 18.1016 27.9996 17.8016 27.7996C17.6016 27.5996 17.4016 27.2996 17.4016 26.8996V17.2996C17.4016 16.8996 17.5016 16.5996 17.8016 16.3996C18.1016 16.1996 18.3016 16.0996 18.7016 16.0996C19.1016 16.0996 19.4016 16.1996 19.6016 16.3996ZM19.9016 10.9996C20.2016 11.2996 20.3016 11.5996 20.3016 11.9996C20.3016 12.3996 20.2016 12.7996 19.9016 12.9996C19.6016 13.2996 19.3016 13.3996 18.8016 13.3996C18.3016 13.3996 18.0016 13.2996 17.7016 12.9996C17.4016 12.6996 17.3016 12.3996 17.3016 11.9996C17.3016 11.5996 17.4016 11.1996 17.7016 10.9996C18.0016 10.6996 18.4016 10.5996 18.8016 10.5996C19.2016 10.5996 19.6016 10.6996 19.9016 10.9996Z"
                    fill="black"
                  />
                </Svg>

                <View
                  style={{
                    width: '83%',
                    paddingHorizontal: 1,
                    marginBottom: 1,
                    borderWidth: 0,
                    marginTop: heightPixel(5),
                  }}>
                  <Text
                    style={{
                      fontSize: calculateFontSize(),
                      color: '#000',
                      lineHeight: calculateLineHeight(),
                      fontFamily: 'Jost-Regular',
                      // marginBottom: 1,
                    }}>
                    For more details,please refer to our
                    <Text
                      style={{ textDecorationLine: 'underline' }}
                      onPress={() => {
                        closeModal();
                        props.onAction({
                          action: 'OPEN_WEBVIEW',
                          params: {
                            url: 'https://www.jockey.in/pages/return-policy',
                            title: 'Return Policy',
                            replacePage: false,
                          },
                        });
                      }}>
                      {' '}
                      Return & Refund Policy.
                    </Text>
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </View >
  );
}

export const fetchData = async (imageIds) => {
  try {

    const [response] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'gqlQuery',
        params: {
          query: `
           {
            nodes(ids: ${JSON.stringify(imageIds)}) {
              ... on MediaImage {
                id
                image {
                  src
                  height
                  altText
                  width
                }
              }
            }
          }
          `
        }
      });

    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

async function fetchProductDataFromId(productId) {
  try {

    const [response] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'gqlQuery',
        params: {
          query: `
          {
            product(id: "${productId}") {
              handle
              title
              compareAtPriceRange {
                minVariantPrice {
                  amount
                  currencyCode
                }
              }
                 priceRange {
                minVariantPrice {
                  amount
                  currencyCode
                }
              }
            }
          }

          `,
        },
      },
    );


    return response.data;
  } catch (error) {
    console.error('Error fetchProductDataFromId data:', error);
  }
}

const renderContent = (content) => {
  const jsonContent = JSON.parse(content);

  const htmlContent = jsonContent.children
    .map((child) =>
      child.children.map((grandChild) => grandChild.value).join(''),
    )
    .join('');

  return htmlContent;
};
const circleSize = wp('10%');
const styles = StyleSheet.create({

  container: {
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    paddingHorizontal: widthPixel(16),
    backgroundColor: 'white',
  },
  paddingContainer: {
    backgroundColor: 'white', // Background color for the padding container
  },

  // gridContainer: {
  //    paddingHorizontal: 5,
  //   marginBottom: 5,
  //   // display:'flex',
  //   // flex: 1,
  //   // flexDirection: 'row',
  //   //   justifyContent: 'space-around',

  //   // overflow:'hidden',
  //    borderWidth:1,
  // },
  gridImage: {
    width: widthPixel(40),
    height: heightPixel(40),
    alignItems: 'center',
    marginRight: widthPixel(3),
    borderRadius: widthPixel(10),
    overflow: 'hidden',
    borderBottomWidth: 0,
  },
  gridImage1: {
    width: widthPixel(110),
    height: heightPixel(120),
    alignItems: 'center',
    marginRight: widthPixel(3),
    borderRadius: widthPixel(10),
    overflow: 'hidden',
    borderBottomWidth: 0,
  },
  video: {
    // width: Dimensions.get('window').width,

    height: heightPixel(150), // Adjust the height as needed
    borderRadius: widthPixel(20),
  },

  scrollViewContent: {
    alignItems: 'center',
  },
  image: {
    width: widthPixel(210), // Adjust the width as needed
    height: heightPixel(250), // Adjust the height as needed
    resizeMode: 'contain', // Adjust the resizeMode as needed
    margin: widthPixel(5),
    borderRadius: widthPixel(10), // see if the image have this already.
  },

  // washing
  washingContainer: {
    borderRadius: widthPixel(15),
    borderWidth: 1,
    borderColor: 'rgba(34,31,32,.24)',
    // marginHorizontal: 7,
    // marginVertical: 15,
    marginTop: heightPixel(5),
    marginBottom: heightPixel(15),
    padding: widthPixel(10),
    backgroundColor: 'white',
  },

  // product highlight
  productContainer: {
    borderRadius: widthPixel(15),
    elevation: 5,
    marginHorizontal: widthPixel(7),
    marginVertical: heightPixel(15),
    padding: widthPixel(10),
    backgroundColor: 'white',
  },
  topSection: {
    flex: 2,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: heightPixel(10),
    marginVertical: heightPixel(10),
    borderBottomWidth: 1,
    borderColor: 'grey',
  },
  heading: {
    fontSize: fonts._24,
    color: '#221f20C7',
  },
  bottomSection: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cardContainer: {
    width: '50%', // Each card takes half of the container width
    padding: widthPixel(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridRow: {
    paddingHorizontal: widthPixel(20),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    overflow: 'hidden',
    borderBottomWidth: 0,
  },
  gridItem: {
    //  flex: 1,
    // marginRight: 2,
    alignItems: 'center',
    borderRadius: widthPixel(10),
    overflow: 'hidden',
    borderBottomWidth: 0,
  },

  // modal
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    flex: 1,
    width: '100%',
    backgroundColor: 'white',
    padding: widthPixel(10),
    // borderRadius: 10,
  },
  bottomModalContent: {
    backgroundColor: 'rgb(254,254,255)',
    width: '100%',
    height: 460,
    borderTopLeftRadius: widthPixel(20),
    borderTopRightRadius: widthPixel(30),
    paddingVertical: heightPixel(20),
    paddingHorizontal: 1,
  },
  closeButton: {
    // alignItems: 'flex-end',
    padding: widthPixel(5),
  },

  bottomCloseButton: {
    width: widthPixel(30),
    height: widthPixel(30),
    borderRadius: widthPixel(15),
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowRadius: widthPixel(2),
    shadowColor: '#000',
    shadowOpacity: 0.1, // Adjust shadow visibility
    shadowOffset: { width: 0, height: widthPixel(2) },
  },

  modalHeader: {
    padding: widthPixel(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: heightPixel(10),
    marginVertical: heightPixel(10),
  },
  modalHeaderText: {
    textAlign: 'left',
    fontSize: fonts._18,
    color: '#000000',
    fontFamily: fonts.FONT_FAMILY.Medium,
  },

  bottomModalHeading: {
    fontSize: fonts._14,
    fontFamily: "Jost-Bold",
    color: '#221F20',
    lineHeight: heightPixel(24),
    letterSpacing: 0.6,
    fontWeight: "700"
  },

  bottomModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: widthPixel(20),
  },

  // policy
  PolicyHeader: {
    // padding:10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    overflow: 'hidden',
    // marginRight:20,
    borderWidth: 0,
    marginTop: heightPixel(20),
  },

  // delivery button

  button: {
    backgroundColor: 'grey',
    paddingHorizontal: widthPixel(18),
    paddingVertical: heightPixel(12),
    borderRadius: widthPixel(5),
  },
  buttonText: {
    fontSize: fonts._16,
    color: 'white',
    textAlign: 'center',
  },

  // description slider

  container22: {
    //  flex: 1,
    // backgroundColor: '#fff',
    // alignItems: 'center',
    // justifyContent: 'center',
  },

  swiperContainer: {
    width: '100%',
    height: heightPixel(350), // Adjust the height as needed
  },

  swiper: {
    overflow: 'visible',
    // marginHorizontal: windowWidth * 0.125, // Center the slides and show 1.5 slides
  },

  slide: {
    // marginRight: windowWidth * 0.01,
    width: windowWidth * 0.66, // Each slide takes 75% of the screen width
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: heightPixel(300),
    borderRadius: widthPixel(10),
    resizeMode: 'contain',
  },
  svgWrapper: {
    position: 'absolute',
    zIndex: -1,
  },

  dot: {
    backgroundColor: 'rgba(255,255,255,.3)',
    width: widthPixel(8),
    height: heightPixel(8),
    borderRadius: widthPixel(4),
    marginHorizontal: widthPixel(3),
  },
  activeDot: {
    backgroundColor: '#ffffff',
    width: widthPixel(8),
    height: heightPixel(8),
    borderRadius: widthPixel(4),
    marginHorizontal: widthPixel(3),
  },

  packOfProductWrapper: {
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'rgba(34,31,32,.17)',
    borderRadius: widthPixel(5),
    alignItems: 'center',
    padding: widthPixel(5),
    justifyContent: 'space-between',
  },

  packOfProductContainer: {
    flex: 1,
    borderColor: 'rgba(34,31,32,.17)',
    borderRadius: widthPixel(10),
    borderWidth: 1,
    borderColor: 'rgba(34, 31, 32, .17)',
    padding: widthPixel(10),
    marginTop: heightPixel(10),
    marginBottom: heightPixel(10),
  },

  packOfProductButtonText: {
    color: '#fff',
    fontFamily: fonts.FONT_FAMILY.Regular,
    fontSize: fonts._12,
  },

  gridContainer: {
    flex: 1,
  },
  listContainer: {
    //  flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around', // Adjust spacing for even distribution
    // alignItems: 'center',
    borderWidth: 1,
    borderColor: 'blue',
  },
  itemContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: widthPixel(100),
    height: heightPixel(100),
    marginVertical: heightPixel(10),
    marginHorizontal: widthPixel(4),
    borderWidth: 1,
    borderColor: 'rgba(34,31,32,.09)',
    borderRadius: widthPixel(10),
  },
  imageContainer: {
    width: widthPixel(36),
    height: heightPixel(36),
    // alignItems: 'center'
  },

  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  textContainer: {
    marginTop: heightPixel(4),
  },
  text: {
    textAlign: 'center',
    fontSize: fonts._15,
    color: '#221f20',
    fontFamily: fonts.FONT_FAMILY.Bold,
    width: widthPixel(90),
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderColor: 'black',
  },
  blinkingCircle: {
    width: circleSize,
    height: circleSize,
    borderRadius: circleSize / 2,
    backgroundColor: '#FFFFF7',
    position: 'absolute',
  },
  svgContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Return Steps Styles
  returnStepContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: heightPixel(20),
    paddingHorizontal: widthPixel(10),
  },
  stepNumberContainer: {
    width: widthPixel(24),
    height: widthPixel(24),
    borderRadius: widthPixel(12),
    backgroundColor: '#221f20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: widthPixel(12),
    marginTop: heightPixel(2),
  },
  stepNumber: {
    color: '#ffffff',
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.SemiBold,
  },
  stepIconContainer: {
    marginRight: widthPixel(12),
    marginTop: heightPixel(-2),
  },
  stepTextContainer: {
    flex: 1,
    paddingTop: heightPixel(8),
  },
  stepTitle: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.SemiBold,
    color: '#221f20',
    marginBottom: heightPixel(2),
  },
  stepDescription: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Regular,
    color: '#221f20',
    lineHeight: heightPixel(20),
  },
});
