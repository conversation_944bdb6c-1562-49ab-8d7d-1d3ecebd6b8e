{"version": 3, "file": "Json2json.js", "sourceRoot": "", "sources": ["../src/Json2json.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAuBA;IA4BE,mBAAmB,QAAkB,EAAE,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;QACnE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACM,6BAAS,GAAhB,UAAiB,IAAI;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC9E,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,OAAO,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SACrC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACO,kCAAc,GAAtB,UAAuB,IAAI,EAAE,QAAkB,EAAE,OAAgB;QAC/D,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAExE,IAAI,YAAY,CAAC,QAAQ,EAAE;YACzB,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;gBACtC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,UAAC,eAAe;oBAC/C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,wBACxC,OAAO,KACV,KAAK,EAAE,eAAe,IACtB,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;oBAC/C,OAAO,SAAS,CAAC,cAAc,CAAC;iBACjC;aACF;SACF;QAED,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,YAAY,CAAC,QAAQ,EAAE;YACtD,IAAM,iBAAe,GACnB,OAAO,YAAY,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAM,OAAA,YAAY,CAAC,QAAQ,EAArB,CAAqB,CAAC;YAEpG,YAAY,CAAC,WAAW,GAAG,UAAC,GAAG,IAAK,OAAA,CAAC,OAAO,GAAG,KAAK,WAAW,CAAC,CAAC,CAAC,iBAAe,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAtD,CAAsD,CAAC;SAC5F;QAED,IAAI,YAAY,CAAC,WAAW,EAAE;YAC5B,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;gBACtC,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAC,eAAe;oBAC5C,OAAO,YAAY,CAAC,WAAW,CAAC,eAAe,wBAC1C,OAAO,KACV,KAAK,EAAE,eAAe,IACtB,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;aAC9D;SACF;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAhB,CAAgB,CAAC,EAAE;YAC7D,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;SACjE;aAAM;YACL,OAAO,WAAW,CAAC;SACpB;IACH,CAAC;IACO,mCAAe,GAAvB,UAAwB,WAAW,EAAE,YAA0B,EAAE,OAAgB;QAAjF,iBA8BC;QA7BC,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAhB,CAAgB,CAAC,CAAC;QAEjF,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;YACtC,IAAI,OAAK,GAAG,CAAC,CAAC;YACd,OAAO,WAAW,CAAC,GAAG,CAAC,UAAC,eAAe;gBACrC,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,YAAY,CAAC,OAAO,CAAC,UAAC,GAAG;oBACvB,IAAM,WAAW,GAAG,KAAI,CAAC,cAAc,CAAC,eAAe,EAAE,YAAY,CAAC,GAAG,CAAC,wBACrE,OAAO,KACV,KAAK,EAAE,eAAe,EACtB,MAAM,EAAE,OAAK,IACb,CAAC;oBACH,IAAI,WAAW,KAAK,SAAS,CAAC,cAAc,EAAE;wBAC5C,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;qBAC3B;oBACD,OAAK,IAAI,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,YAAY,CAAC,OAAO,CAAC,UAAC,GAAG;YACvB,IAAM,WAAW,GAAG,KAAI,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;YACjF,IAAI,WAAW,KAAK,SAAS,CAAC,cAAc,EAAE;gBAC5C,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;aAC3B;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,2CAA2C;IAC3C,sEAAsE;IAC9D,iCAAa,GAArB,UAAsB,IAAI,EAAE,IAAuB,EAAE,OAAgB;QAArE,iBA2CC;;QA1CC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAClD,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvE,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YAC5B,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAC1D;QACD,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YAC5B,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAC9D;QACD,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YACnC,IAAI,UAAU,KAAK,OAAO,EAAE;gBAC1B,MAAM,SAAG,MAAM,CAAC,CAAC,CAAC,mCAAI,IAAI,CAAC;gBAC3B,SAAS;aACV;YACD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC5B,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAC1B,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC3C,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE;wBAC7C,OAAO,EAAE,CAAC;qBACX;iBACF;gBACD,MAAM,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzD,OAAO,MAAM,CAAC,GAAG,CAAC,UAAC,QAAQ;oBACzB,OAAO,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,wBACxC,OAAO,KACV,KAAK,EAAE,QAAQ,IACf,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC1B,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC3C,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE;oBAC7C,OAAO,SAAS,CAAC;iBAClB;aACF;YACD,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;SAC7B;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACO,mCAAe,GAAvB,UAAwB,QAAkB;QACxC,IAAI,YAAY,GAAiB;YAC/B,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,YAAY,CAAC,KAAK,GAAG,QAAQ,CAAC;SAC/B;aAAM,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YACzC,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;SACrC;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YACvC,YAAY,yBACP,YAAY,GACZ,QAAQ,CACZ,CAAC;SACH;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IACO,mCAAe,GAAvB,UAAwB,QAAsB;QAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IArLc,wBAAc,GAAG,oBAAoB,CAAC;IACvC,oBAAU,GAAG,UAAC,IAAI;QAC9B,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,IAAI,EAAE;YAChD,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,KAAK,SAAS,EAAtB,CAAsB,CAAC,CAAC;YAChG,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,SAAS,CAAC;YAC/C,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,GAAG;;gBACrD,IAAM,eAAe,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxD,IAAI,OAAO,eAAe,KAAK,WAAW;oBAAE,OAAO,IAAI,CAAC;gBACxD,6BACK,IAAI,gBACN,GAAG,IAAG,eAAe,OACtB;YACJ,CAAC,EAAE,EAAE,CAAC,CAAC;YACP,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,SAAS,CAAC;YAC5D,OAAO,WAAW,CAAC;SACpB;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IA+JJ,gBAAC;CAAA,AAvLD,IAuLC;kBAvLoB,SAAS"}