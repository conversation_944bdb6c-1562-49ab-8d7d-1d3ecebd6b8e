{"version": 3, "names": ["formatters", "require", "_builder", "smart", "createTemplateBuilder", "exports", "statement", "statements", "expression", "program", "_default", "Object", "assign", "bind", "undefined", "ast", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as formatters from \"./formatters.ts\";\nimport createTemplateBuilder from \"./builder.ts\";\n\nexport const smart = createTemplateBuilder(formatters.smart);\nexport const statement = createTemplateBuilder(formatters.statement);\nexport const statements = createTemplateBuilder(formatters.statements);\nexport const expression = createTemplateBuilder(formatters.expression);\nexport const program = createTemplateBuilder(formatters.program);\n\ntype DefaultTemplateBuilder = typeof smart & {\n  smart: typeof smart;\n  statement: typeof statement;\n  statements: typeof statements;\n  expression: typeof expression;\n  program: typeof program;\n};\n\nexport default Object.assign(smart.bind(undefined) as DefaultTemplateBuilder, {\n  smart,\n  statement,\n  statements,\n  expression,\n  program,\n  ast: smart.ast,\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAEO,MAAME,KAAK,GAAG,IAAAC,gBAAqB,EAACJ,UAAU,CAACG,KAAK,CAAC;AAACE,OAAA,CAAAF,KAAA,GAAAA,KAAA;AACtD,MAAMG,SAAS,GAAG,IAAAF,gBAAqB,EAACJ,UAAU,CAACM,SAAS,CAAC;AAACD,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAC9D,MAAMC,UAAU,GAAG,IAAAH,gBAAqB,EAACJ,UAAU,CAACO,UAAU,CAAC;AAACF,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAChE,MAAMC,UAAU,GAAG,IAAAJ,gBAAqB,EAACJ,UAAU,CAACQ,UAAU,CAAC;AAACH,OAAA,CAAAG,UAAA,GAAAA,UAAA;AAChE,MAAMC,OAAO,GAAG,IAAAL,gBAAqB,EAACJ,UAAU,CAACS,OAAO,CAAC;AAACJ,OAAA,CAAAI,OAAA,GAAAA,OAAA;AAAA,IAAAC,QAAA,GAUlDC,MAAM,CAACC,MAAM,CAACT,KAAK,CAACU,IAAI,CAACC,SAAS,CAAC,EAA4B;EAC5EX,KAAK;EACLG,SAAS;EACTC,UAAU;EACVC,UAAU;EACVC,OAAO;EACPM,GAAG,EAAEZ,KAAK,CAACY;AACb,CAAC,CAAC;AAAAV,OAAA,CAAAW,OAAA,GAAAN,QAAA"}