{"version": 3, "names": ["_core", "require", "exports", "getDynamicImportSource", "node", "source", "arguments", "t", "isStringLiteral", "isTemplateLiteral", "template", "expression", "ast", "buildDynamicImport", "deferT<PERSON><PERSON><PERSON>", "wrapWithPromise", "builder", "specifier", "quasis", "length", "specifierToString", "identifier", "templateLiteral", "templateElement", "raw"], "sources": ["../src/dynamic-import.ts"], "sourcesContent": ["// Heavily inspired by\n// https://github.com/airbnb/babel-plugin-dynamic-import-node/blob/master/src/utils.js\n\nimport { types as t, template } from \"@babel/core\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  if (!USE_ESM) {\n    if (!IS_STANDALONE) {\n      // eslint-disable-next-line no-restricted-globals\n      exports.getDynamicImportSource = function getDynamicImportSource(\n        node: t.CallExpression,\n      ): t.StringLiteral | t.TemplateLiteral {\n        const [source] = node.arguments;\n\n        return t.isStringLiteral(source) || t.isTemplateLiteral(source)\n          ? source\n          : (template.expression.ast`\\`\\${${source}}\\`` as t.TemplateLiteral);\n      };\n    }\n  }\n}\n\nexport function buildDynamicImport(\n  node: t.CallExpression,\n  deferToThen: boolean,\n  wrapWithPromise: boolean,\n  builder: (specifier: t.Expression) => t.Expression,\n): t.Expression {\n  const [specifier] = node.arguments;\n\n  if (\n    t.isStringLiteral(specifier) ||\n    (t.isTemplateLiteral(specifier) && specifier.quasis.length === 0)\n  ) {\n    if (deferToThen) {\n      return template.expression.ast`\n        Promise.resolve().then(() => ${builder(specifier)})\n      `;\n    } else return builder(specifier);\n  }\n\n  const specifierToString = t.isTemplateLiteral(specifier)\n    ? t.identifier(\"specifier\")\n    : t.templateLiteral(\n        [t.templateElement({ raw: \"\" }), t.templateElement({ raw: \"\" })],\n        [t.identifier(\"specifier\")],\n      );\n\n  if (deferToThen) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${specifierToString}))\n          .then(s => ${builder(t.identifier(\"s\"))})\n      )(${specifier})\n    `;\n  } else if (wrapWithPromise) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${builder(specifierToString)}))\n      )(${specifier})\n    `;\n  } else {\n    return template.expression.ast`\n      (specifier => ${builder(specifierToString)})(${specifier})\n    `;\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAEmC;EACnB;IACQ;MAElBC,OAAO,CAACC,sBAAsB,GAAG,SAASA,sBAAsBA,CAC9DC,IAAsB,EACe;QACrC,MAAM,CAACC,MAAM,CAAC,GAAGD,IAAI,CAACE,SAAS;QAE/B,OAAOC,WAAC,CAACC,eAAe,CAACH,MAAM,CAAC,IAAIE,WAAC,CAACE,iBAAiB,CAACJ,MAAM,CAAC,GAC3DA,MAAM,GACLK,cAAQ,CAACC,UAAU,CAACC,GAAI,QAAOP,MAAO,KAA0B;MACvE,CAAC;IACH;EACF;AACF;AAEO,SAASQ,kBAAkBA,CAChCT,IAAsB,EACtBU,WAAoB,EACpBC,eAAwB,EACxBC,OAAkD,EACpC;EACd,MAAM,CAACC,SAAS,CAAC,GAAGb,IAAI,CAACE,SAAS;EAElC,IACEC,WAAC,CAACC,eAAe,CAACS,SAAS,CAAC,IAC3BV,WAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,IAAIA,SAAS,CAACC,MAAM,CAACC,MAAM,KAAK,CAAE,EACjE;IACA,IAAIL,WAAW,EAAE;MACf,OAAOJ,cAAQ,CAACC,UAAU,CAACC,GAAI;AACrC,uCAAuCI,OAAO,CAACC,SAAS,CAAE;AAC1D,OAAO;IACH,CAAC,MAAM,OAAOD,OAAO,CAACC,SAAS,CAAC;EAClC;EAEA,MAAMG,iBAAiB,GAAGb,WAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,GACpDV,WAAC,CAACc,UAAU,CAAC,WAAW,CAAC,GACzBd,WAAC,CAACe,eAAe,CACf,CAACf,WAAC,CAACgB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,EAAEjB,WAAC,CAACgB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,CAAC,EAChE,CAACjB,WAAC,CAACc,UAAU,CAAC,WAAW,CAAC,CAC5B,CAAC;EAEL,IAAIP,WAAW,EAAE;IACf,OAAOJ,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnC;AACA,6BAA6BQ,iBAAkB;AAC/C,uBAAuBJ,OAAO,CAACT,WAAC,CAACc,UAAU,CAAC,GAAG,CAAC,CAAE;AAClD,UAAUJ,SAAU;AACpB,KAAK;EACH,CAAC,MAAM,IAAIF,eAAe,EAAE;IAC1B,OAAOL,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnC;AACA,6BAA6BI,OAAO,CAACI,iBAAiB,CAAE;AACxD,UAAUH,SAAU;AACpB,KAAK;EACH,CAAC,MAAM;IACL,OAAOP,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnC,sBAAsBI,OAAO,CAACI,iBAAiB,CAAE,KAAIH,SAAU;AAC/D,KAAK;EACH;AACF"}