/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { Config } from '@jest/types';
import type { ShouldInstrumentOptions } from './types';
export default function shouldInstrument(filename: Config.Path, options: ShouldInstrumentOptions, config: Config.ProjectConfig): boolean;
