{"version": 3, "names": ["_jsTokens", "require", "_helperValidatorIdentifier", "_chalk", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "sometimesKeywords", "Set", "getDefs", "chalk", "keyword", "cyan", "capitalized", "yellow", "jsxIdentifier", "punctuator", "number", "magenta", "string", "green", "regex", "comment", "grey", "invalid", "white", "bgRed", "bold", "NEWLINE", "BRACKET", "tokenize", "JSX_TAG", "getTokenType", "token", "offset", "text", "type", "isKeyword", "value", "isStrictReservedWord", "test", "slice", "toLowerCase", "match", "jsTokens", "exec", "matchToToken", "index", "highlightTokens", "defs", "highlighted", "colorize", "split", "map", "str", "join", "should<PERSON><PERSON><PERSON>", "options", "level", "forceColor", "chalkWithForcedColor", "undefined", "getChalk", "_chalkWithForcedColor", "constructor", "enabled", "exports", "highlight", "code"], "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"../../../lib/third-party-libs.d.ts\" />\n\nimport type { Token as <PERSON>SToken, JSXToken } from \"js-tokens\";\nimport jsTokens from \"js-tokens\";\n\nimport {\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\nimport chalk, { Chalk as ChalkClass, type <PERSON><PERSON>Inst<PERSON> as Chalk } from \"chalk\";\n\n/**\n * Names that are always allowed as identifiers, but also appear as keywords\n * within certain syntactic productions.\n *\n * https://tc39.es/ecma262/#sec-keywords-and-reserved-words\n *\n * `target` has been omitted since it is very likely going to be a false\n * positive.\n */\nconst sometimesKeywords = new Set([\"as\", \"async\", \"from\", \"get\", \"of\", \"set\"]);\n\ntype InternalTokenType =\n  | \"keyword\"\n  | \"capitalized\"\n  | \"jsxIdentifier\"\n  | \"punctuator\"\n  | \"number\"\n  | \"string\"\n  | \"regex\"\n  | \"comment\"\n  | \"invalid\";\n\ntype Token = {\n  type: InternalTokenType | \"uncolored\";\n  value: string;\n};\n/**\n * Chalk styles for token types.\n */\nfunction getDefs(chalk: Chalk): Record<InternalTokenType, Chalk> {\n  return {\n    keyword: chalk.cyan,\n    capitalized: chalk.yellow,\n    jsxIdentifier: chalk.yellow,\n    punctuator: chalk.yellow,\n    number: chalk.magenta,\n    string: chalk.green,\n    regex: chalk.magenta,\n    comment: chalk.grey,\n    invalid: chalk.white.bgRed.bold,\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * RegExp to test for the three types of brackets.\n */\nconst BRACKET = /^[()[\\]{}]$/;\n\nlet tokenize: (\n  text: string,\n) => Generator<{ type: InternalTokenType | \"uncolored\"; value: string }>;\n\nif (process.env.BABEL_8_BREAKING) {\n  /**\n   * Get the type of token, specifying punctuator type.\n   */\n  const getTokenType = function (\n    token: JSToken | JSXToken,\n  ): InternalTokenType | \"uncolored\" {\n    if (token.type === \"IdentifierName\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"Punctuator\" && BRACKET.test(token.value)) {\n      return \"uncolored\";\n    }\n\n    if (token.type === \"Invalid\" && token.value === \"@\") {\n      return \"punctuator\";\n    }\n\n    switch (token.type) {\n      case \"NumericLiteral\":\n        return \"number\";\n\n      case \"StringLiteral\":\n      case \"JSXString\":\n      case \"NoSubstitutionTemplate\":\n        return \"string\";\n\n      case \"RegularExpressionLiteral\":\n        return \"regex\";\n\n      case \"Punctuator\":\n      case \"JSXPunctuator\":\n        return \"punctuator\";\n\n      case \"MultiLineComment\":\n      case \"SingleLineComment\":\n        return \"comment\";\n\n      case \"Invalid\":\n      case \"JSXInvalid\":\n        return \"invalid\";\n\n      case \"JSXIdentifier\":\n        return \"jsxIdentifier\";\n\n      default:\n        return \"uncolored\";\n    }\n  };\n\n  /**\n   * Turn a string of JS into an array of objects.\n   */\n  tokenize = function* (text: string): Generator<Token> {\n    for (const token of jsTokens(text, { jsx: true })) {\n      switch (token.type) {\n        case \"TemplateHead\":\n          yield { type: \"string\", value: token.value.slice(0, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateMiddle\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateTail\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1) };\n          break;\n\n        default:\n          yield {\n            type: getTokenType(token),\n            value: token.value,\n          };\n      }\n    }\n  };\n} else {\n  /**\n   * RegExp to test for what seems to be a JSX tag name.\n   */\n  const JSX_TAG = /^[a-z][\\w-]*$/i;\n\n  // The token here is defined in js-tokens@4. However we don't bother\n  // typing it since the whole block will be removed in Babel 8\n  const getTokenType = function (token: any, offset: number, text: string) {\n    if (token.type === \"name\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (\n        JSX_TAG.test(token.value) &&\n        (text[offset - 1] === \"<\" || text.slice(offset - 2, offset) == \"</\")\n      ) {\n        return \"jsxIdentifier\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"punctuator\" && BRACKET.test(token.value)) {\n      return \"bracket\";\n    }\n\n    if (\n      token.type === \"invalid\" &&\n      (token.value === \"@\" || token.value === \"#\")\n    ) {\n      return \"punctuator\";\n    }\n\n    return token.type;\n  };\n\n  tokenize = function* (text: string) {\n    let match;\n    while ((match = (jsTokens as any).default.exec(text))) {\n      const token = (jsTokens as any).matchToToken(match);\n\n      yield {\n        type: getTokenType(token, match.index, text),\n        value: token.value,\n      };\n    }\n  };\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\nfunction highlightTokens(defs: Record<string, Chalk>, text: string) {\n  let highlighted = \"\";\n\n  for (const { type, value } of tokenize(text)) {\n    const colorize = defs[type];\n    if (colorize) {\n      highlighted += value\n        .split(NEWLINE)\n        .map(str => colorize(str))\n        .join(\"\\n\");\n    } else {\n      highlighted += value;\n    }\n  }\n\n  return highlighted;\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\n\ntype Options = {\n  forceColor?: boolean;\n};\n\n/**\n * Whether the code should be highlighted given the passed options.\n */\nexport function shouldHighlight(options: Options): boolean {\n  return chalk.level > 0 || options.forceColor;\n}\n\nlet chalkWithForcedColor: Chalk = undefined;\nfunction getChalk(forceColor: boolean) {\n  if (forceColor) {\n    chalkWithForcedColor ??= process.env.BABEL_8_BREAKING\n      ? new ChalkClass({ level: 1 })\n      : // @ts-expect-error .Instance was .constructor in chalk 2\n        new chalk.constructor({ enabled: true, level: 1 });\n    return chalkWithForcedColor;\n  }\n  return chalk;\n}\nif (!process.env.BABEL_8_BREAKING) {\n  if (!USE_ESM) {\n    // eslint-disable-next-line no-restricted-globals\n    exports.getChalk = (options: Options) => getChalk(options.forceColor);\n  }\n}\n\n/**\n * Highlight `code`.\n */\nexport default function highlight(code: string, options: Options = {}): string {\n  if (code !== \"\" && shouldHighlight(options)) {\n    const defs = getDefs(getChalk(options.forceColor));\n    return highlightTokens(defs, code);\n  } else {\n    return code;\n  }\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,0BAAA,GAAAD,OAAA;AAKA,IAAAE,MAAA,GAAAC,uBAAA,CAAAH,OAAA;AAAgF,SAAAI,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAWhF,MAAMW,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAoB9E,SAASC,OAAOA,CAACC,KAAY,EAAoC;EAC/D,OAAO;IACLC,OAAO,EAAED,KAAK,CAACE,IAAI;IACnBC,WAAW,EAAEH,KAAK,CAACI,MAAM;IACzBC,aAAa,EAAEL,KAAK,CAACI,MAAM;IAC3BE,UAAU,EAAEN,KAAK,CAACI,MAAM;IACxBG,MAAM,EAAEP,KAAK,CAACQ,OAAO;IACrBC,MAAM,EAAET,KAAK,CAACU,KAAK;IACnBC,KAAK,EAAEX,KAAK,CAACQ,OAAO;IACpBI,OAAO,EAAEZ,KAAK,CAACa,IAAI;IACnBC,OAAO,EAAEd,KAAK,CAACe,KAAK,CAACC,KAAK,CAACC;EAC7B,CAAC;AACH;AAKA,MAAMC,OAAO,GAAG,yBAAyB;AAKzC,MAAMC,OAAO,GAAG,aAAa;AAE7B,IAAIC,QAEoE;AA6FjE;EAIL,MAAMC,OAAO,GAAG,gBAAgB;EAIhC,MAAMC,YAAY,GAAG,SAAAA,CAAUC,KAAU,EAAEC,MAAc,EAAEC,IAAY,EAAE;IACvE,IAAIF,KAAK,CAACG,IAAI,KAAK,MAAM,EAAE;MACzB,IACE,IAAAC,oCAAS,EAACJ,KAAK,CAACK,KAAK,CAAC,IACtB,IAAAC,+CAAoB,EAACN,KAAK,CAACK,KAAK,EAAE,IAAI,CAAC,IACvC/B,iBAAiB,CAACb,GAAG,CAACuC,KAAK,CAACK,KAAK,CAAC,EAClC;QACA,OAAO,SAAS;MAClB;MAEA,IACEP,OAAO,CAACS,IAAI,CAACP,KAAK,CAACK,KAAK,CAAC,KACxBH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIC,IAAI,CAACM,KAAK,CAACP,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,IAAI,IAAI,CAAC,EACpE;QACA,OAAO,eAAe;MACxB;MAEA,IAAID,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,KAAKL,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EAAE;QACnD,OAAO,aAAa;MACtB;IACF;IAEA,IAAIT,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIP,OAAO,CAACW,IAAI,CAACP,KAAK,CAACK,KAAK,CAAC,EAAE;MAC5D,OAAO,SAAS;IAClB;IAEA,IACEL,KAAK,CAACG,IAAI,KAAK,SAAS,KACvBH,KAAK,CAACK,KAAK,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,KAAK,GAAG,CAAC,EAC5C;MACA,OAAO,YAAY;IACrB;IAEA,OAAOL,KAAK,CAACG,IAAI;EACnB,CAAC;EAEDN,QAAQ,GAAG,UAAAA,CAAWK,IAAY,EAAE;IAClC,IAAIQ,KAAK;IACT,OAAQA,KAAK,GAAIC,SAAQ,CAASpD,OAAO,CAACqD,IAAI,CAACV,IAAI,CAAC,EAAG;MACrD,MAAMF,KAAK,GAAIW,SAAQ,CAASE,YAAY,CAACH,KAAK,CAAC;MAEnD,MAAM;QACJP,IAAI,EAAEJ,YAAY,CAACC,KAAK,EAAEU,KAAK,CAACI,KAAK,EAAEZ,IAAI,CAAC;QAC5CG,KAAK,EAAEL,KAAK,CAACK;MACf,CAAC;IACH;EACF,CAAC;AACH;AAKA,SAASU,eAAeA,CAACC,IAA2B,EAAEd,IAAY,EAAE;EAClE,IAAIe,WAAW,GAAG,EAAE;EAEpB,KAAK,MAAM;IAAEd,IAAI;IAAEE;EAAM,CAAC,IAAIR,QAAQ,CAACK,IAAI,CAAC,EAAE;IAC5C,MAAMgB,QAAQ,GAAGF,IAAI,CAACb,IAAI,CAAC;IAC3B,IAAIe,QAAQ,EAAE;MACZD,WAAW,IAAIZ,KAAK,CACjBc,KAAK,CAACxB,OAAO,CAAC,CACdyB,GAAG,CAACC,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CACzBC,IAAI,CAAC,IAAI,CAAC;IACf,CAAC,MAAM;MACLL,WAAW,IAAIZ,KAAK;IACtB;EACF;EAEA,OAAOY,WAAW;AACpB;AAaO,SAASM,eAAeA,CAACC,OAAgB,EAAW;EACzD,OAAO/C,cAAK,CAACgD,KAAK,GAAG,CAAC,IAAID,OAAO,CAACE,UAAU;AAC9C;AAEA,IAAIC,oBAA2B,GAAGC,SAAS;AAC3C,SAASC,QAAQA,CAACH,UAAmB,EAAE;EACrC,IAAIA,UAAU,EAAE;IAAA,IAAAI,qBAAA;IACd,CAAAA,qBAAA,GAAAH,oBAAoB,YAAAG,qBAAA,GAApBH,oBAAoB,GAGhB,IAAIlD,cAAK,CAACsD,WAAW,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAE,CAAC,CAAC;IACtD,OAAOE,oBAAoB;EAC7B;EACA,OAAOlD,cAAK;AACd;AACmC;EACnB;IAEZwD,OAAO,CAACJ,QAAQ,GAAIL,OAAgB,IAAKK,QAAQ,CAACL,OAAO,CAACE,UAAU,CAAC;EACvE;AACF;AAKe,SAASQ,SAASA,CAACC,IAAY,EAAEX,OAAgB,GAAG,CAAC,CAAC,EAAU;EAC7E,IAAIW,IAAI,KAAK,EAAE,IAAIZ,eAAe,CAACC,OAAO,CAAC,EAAE;IAC3C,MAAMR,IAAI,GAAGxC,OAAO,CAACqD,QAAQ,CAACL,OAAO,CAACE,UAAU,CAAC,CAAC;IAClD,OAAOX,eAAe,CAACC,IAAI,EAAEmB,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,OAAOA,IAAI;EACb;AACF"}